@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 17, 24, 39;
  --background-end-rgb: 31, 41, 55;
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.animate-gradient-shift {
  animation: gradient-shift 15s ease infinite;
}

body {
  color: rgb(var(--foreground-rgb));
  background:
    linear-gradient(
      to bottom right,
      rgb(var(--background-start-rgb)),
      rgb(var(--background-end-rgb))
    ),
    radial-gradient(
      circle at 1px 1px,
      rgba(255,255,255,0.05) 1px,
      transparent 1px
    );
  background-size: 100% 100%, 30px 30px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}
::-webkit-scrollbar-track {
  background: transparent;
}
::-webkit-scrollbar-thumb {
  background: rgba(255,255,255,0.2);
  border-radius: 4px;
}

/* Focus styles */
a, button, input, textarea {
  @apply focus-visible:outline focus-visible:outline-2 focus-visible:outline-indigo-500 focus-visible:outline-offset-2;
}

/* Smooth transitions */
* {
  @apply transition-colors duration-200;
}

/* Sample Frontend Design System Classes */
@layer components {
  /* Card Components */
  .sample-card {
    @apply bg-gray-800 rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-indigo-500/20 hover:ring-2 hover:ring-indigo-500 flex flex-col cursor-pointer hover:-translate-y-1;
  }

  .sample-card-static {
    @apply bg-gray-800 rounded-xl shadow-lg overflow-hidden border border-gray-700;
  }

  /* Button Styles */
  .sample-btn-primary {
    @apply flex items-center justify-center gap-2 bg-indigo-600 hover:bg-indigo-500 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition-transform transform hover:scale-105;
  }

  .sample-btn-secondary {
    @apply px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-indigo-500 bg-gray-700 text-gray-300 hover:bg-gray-600;
  }

  /* Tab Navigation */
  .sample-tab-button {
    @apply px-3 py-2 text-sm sm:text-base font-medium rounded-t-lg transition-colors duration-200 flex items-center gap-2;
  }

  .sample-tab-active {
    @apply border-b-2 border-indigo-500 text-white;
  }

  .sample-tab-inactive {
    @apply border-b-2 border-transparent text-gray-400 hover:text-white;
  }

  /* Status Badges */
  .sample-status-active {
    @apply bg-green-500 text-green-50 ring-1 ring-inset ring-green-600/20;
  }

  .sample-status-completed {
    @apply bg-gray-500 text-gray-100 ring-1 ring-inset ring-gray-500/20;
  }

  .sample-status-pending {
    @apply bg-yellow-500 text-yellow-900 ring-1 ring-inset ring-yellow-600/20;
  }

  .sample-status-in-progress {
    @apply bg-yellow-400/10 text-yellow-300 ring-1 ring-inset ring-yellow-400/30;
  }

  .sample-status-upcoming {
    @apply bg-blue-500/10 text-blue-300 ring-1 ring-inset ring-blue-500/30;
  }

  /* Input Styles */
  .sample-input {
    @apply block w-full bg-gray-700 border border-transparent rounded-md py-2.5 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm;
  }

  /* Header Styles */
  .sample-header {
    @apply bg-gray-800/50 backdrop-blur-sm shadow-lg sticky top-0 z-10;
  }

  /* Progress Bar */
  .sample-progress-bar {
    @apply w-full bg-gray-700 rounded-full h-3.5;
  }

  .sample-progress-fill {
    @apply bg-gradient-to-r from-green-400 to-green-600 h-3.5 rounded-full transition-all duration-500;
  }

  /* Empty State */
  .sample-empty-state {
    @apply text-center py-16 mt-8 bg-gray-800/50 rounded-lg border border-gray-700;
  }
}
