
import React from 'react';
import { User } from '../types';

interface AvatarProps {
  user: User | null;
  size?: 'xs' | 'sm' | 'md' | 'lg';
  className?: string;
}

const sizeClasses = {
  xs: 'h-6 w-6 text-xs',
  sm: 'h-8 w-8 text-sm ring-2 ring-gray-800',
  md: 'h-10 w-10 text-base',
  lg: 'h-12 w-12 text-lg',
};

const colorPalette = [
    'bg-red-500', 'bg-orange-500', 'bg-amber-500', 'bg-yellow-500', 
    'bg-lime-500', 'bg-green-500', 'bg-emerald-500', 'bg-teal-500', 
    'bg-cyan-500', 'bg-sky-500', 'bg-blue-500', 'bg-indigo-500', 
    'bg-violet-500', 'bg-purple-500', 'bg-fuchsia-500', 'bg-pink-500', 'bg-rose-500'
];

const getInitials = (name: string): string => {
  const names = name.split(' ');
  if (names.length > 1) {
    return `${names[0][0]}${names[names.length - 1][0]}`;
  }
  return name.substring(0, 2);
};

const hashCode = (str: string): number => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return hash;
};

const Avatar: React.FC<AvatarProps> = ({ user, size = 'md', className = '' }) => {
  const baseClasses = 'rounded-full flex items-center justify-center font-bold text-white flex-shrink-0';
  const finalSizeClass = sizeClasses[size];

  if (!user) {
    // Render a placeholder for unassigned
    return (
        <div className={`${baseClasses} ${finalSizeClass} bg-gray-600 ${className}`}>
            ?
        </div>
    )
  }

  if (user.avatarUrl) {
    return (
      <img
        className={`${baseClasses} ${finalSizeClass} object-cover ${className}`}
        src={user.avatarUrl}
        alt={user.name}
        title={`${user.name} (${user.email})`}
      />
    );
  }

  const colorIndex = Math.abs(hashCode(user.id)) % colorPalette.length;
  const colorClass = colorPalette[colorIndex];

  return (
    <div 
        className={`${baseClasses} ${finalSizeClass} ${colorClass} ${className}`}
        title={`${user.name} (${user.email})`}
    >
      {getInitials(user.name)}
    </div>
  );
};

export default Avatar;
