
import React from 'react';
import { Job, Shift, User, TimeEntry } from '../types';
import { DownloadIcon, UploadIcon, DocumentTextIcon } from './IconComponents';

interface ImportExportPageProps {
  jobs: Job[];
  shifts: Shift[];
  users: User[];
}

const ImportExportPage: React.FC<ImportExportPageProps> = ({ jobs, shifts, users }) => {

  const downloadCSV = (content: string, filename: string) => {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement("a");
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const exportJobs = () => {
    const headers = ["Job ID", "Job Name", "Company", "Status", "Description", "Requested Workers", "Total Shifts"];
    const rows = jobs.map(job => [
      job.id,
      `"${job.name.replace(/"/g, '""')}"`,
      `"${job.company.name.replace(/"/g, '""')}"`,
      job.status,
      `"${job.description.replace(/"/g, '""')}"`,
      job.requestedWorkers,
      job.shifts.length
    ]);
    const csvContent = [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
    downloadCSV(csvContent, 'jobs_export.csv');
  };

  const exportShifts = () => {
    const headers = ["Shift ID", "Job Name", "Company", "Date", "Start Time", "End Time", "Status", "Total Assignments", "Filled Assignments"];
    const rows = shifts.map(shift => [
      shift.id,
      `"${shift.job.name.replace(/"/g, '""')}"`,
      `"${shift.job.company.name.replace(/"/g, '""')}"`,
      shift.date,
      shift.startTime,
      shift.endTime,
      shift.status,
      shift.assignments.length,
      shift.assignments.filter(a => a.userId).length
    ]);
    const csvContent = [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
    downloadCSV(csvContent, 'shifts_export.csv');
  };
  
  const exportTimesheets = () => {
    const headers = ["User Name", "User Email", "Job Name", "Shift Date", "Role", "Clock In", "Clock Out", "Duration (Hours)"];
    const rows: string[][] = [];
    
    shifts.forEach(shift => {
        shift.assignments.forEach(assignment => {
            if (assignment.userId && assignment.timeEntries.length > 0) {
                const user = users.find(u => u.id === assignment.userId);
                if (user) {
                    assignment.timeEntries.forEach(entry => {
                        if (entry.clockIn && entry.clockOut) {
                            const durationMs = new Date(entry.clockOut).getTime() - new Date(entry.clockIn).getTime();
                            const durationHours = (durationMs / (1000 * 60 * 60)).toFixed(4);
                            rows.push([
                                `"${user.name}"`,
                                user.email,
                                `"${shift.job.name}"`,
                                shift.date,
                                assignment.role,
                                new Date(entry.clockIn).toLocaleString(),
                                new Date(entry.clockOut).toLocaleString(),
                                durationHours,
                            ]);
                        }
                    });
                }
            }
        });
    });
    
    const csvContent = [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
    downloadCSV(csvContent, 'timesheets_export.csv');
  };

  const ActionCard: React.FC<{icon: React.ReactNode, title: string, description: string, buttonText: string, onClick: () => void}> = 
    ({ icon, title, description, buttonText, onClick }) => (
    <div className="bg-gray-800 p-6 rounded-lg shadow-md border border-gray-700/50 flex flex-col items-start">
        <div className="flex items-center gap-4 mb-3">
            {icon}
            <h3 className="text-xl font-bold text-white">{title}</h3>
        </div>
        <p className="text-gray-400 text-sm flex-grow mb-6">{description}</p>
        <button
            onClick={onClick}
            className="w-full mt-auto flex items-center justify-center gap-2 bg-indigo-600 hover:bg-indigo-500 text-white font-semibold py-2.5 px-4 rounded-lg shadow-md transition-transform transform hover:scale-105"
        >
           {buttonText}
        </button>
    </div>
  );

  return (
    <div className="bg-gray-800/50 rounded-xl p-4 sm:p-6 lg:p-8 border border-gray-700">
      <div className="flex items-center gap-3 mb-8">
        <DocumentTextIcon className="h-8 w-8 text-indigo-400" />
        <div>
          <h1 className="text-2xl font-bold text-white">Import & Export Data</h1>
          <p className="text-gray-400">Manage your application data by importing or exporting it in CSV format.</p>
        </div>
      </div>

      <div className="mb-12">
        <h2 className="text-lg font-semibold text-white mb-4 pb-2 border-b border-gray-700">Export Data</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <ActionCard 
            icon={<DownloadIcon className="h-6 w-6 text-green-400"/>}
            title="Export Jobs"
            description="Download a CSV file of all jobs, including their status, company, and description."
            buttonText="Download Jobs CSV"
            onClick={exportJobs}
          />
          <ActionCard 
            icon={<DownloadIcon className="h-6 w-6 text-green-400"/>}
            title="Export Shifts"
            description="Download a CSV file of all shifts, including dates, times, and assignment counts."
            buttonText="Download Shifts CSV"
            onClick={exportShifts}
          />
          <ActionCard 
            icon={<DownloadIcon className="h-6 w-6 text-green-400"/>}
            title="Export Timesheets"
            description="Download a detailed CSV file of all completed time entries for payroll and records."
            buttonText="Download Timesheets CSV"
            onClick={exportTimesheets}
          />
        </div>
      </div>
      
       <div>
        <h2 className="text-lg font-semibold text-white mb-4 pb-2 border-b border-gray-700">Import Data</h2>
        <div className="bg-gray-800 p-8 rounded-lg text-center border-2 border-dashed border-gray-600">
            <UploadIcon className="h-10 w-10 text-gray-500 mx-auto mb-4"/>
            <h3 className="text-xl font-bold text-gray-300">Import functionality coming soon</h3>
            <p className="text-gray-500 mt-2">The ability to import data from CSV files is under development.</p>
        </div>
      </div>
    </div>
  );
};

export default ImportExportPage;
