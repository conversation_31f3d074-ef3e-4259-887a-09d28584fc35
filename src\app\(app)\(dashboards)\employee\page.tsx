'use client';

import { useUser } from '@/hooks/use-user';
import { useApi } from '@/hooks/use-api';
import Link from 'next/link';
import Header from '@/components/Header';
import ShiftCard from '@/components/ShiftCard';
import { CalendarIcon, ClockIcon, PlusIcon } from '@/components/IconComponents';
import { Shift, Job, Company } from '@prisma/client';

type UpcomingShift = Shift & {
  job: Job & {
    company: Company;
  };
};

type DashboardData = {
  upcomingShifts: UpcomingShift[];
};

// Convert Prisma shift to ShiftCard format
const convertShiftForCard = (shift: UpcomingShift) => ({
  id: shift.id,
  jobName: shift.job.name,
  companyName: shift.job.company.name,
  date: shift.date.toISOString().split('T')[0],
  startTime: shift.startTime,
  endTime: shift.endTime,
  status: shift.status,
  assignments: [] // Employee dashboard doesn't need assignment details
});

export default function EmployeeDashboard() {
  const { user } = useUser();
  const { data, loading, error } = useApi<DashboardData>(
    user?.id ? `/api/employees/${user.id}/dashboard` : null
  );

  const handleShiftClick = (shift: any) => {
    // Navigate to shift details or handle shift selection
    console.log('Shift clicked:', shift);
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div className="text-red-500">Error loading dashboard data.</div>;
  }

  const upcomingShifts = data?.upcomingShifts || [];

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100 font-sans">
      <Header>
        <Link href="/timesheet" className="sample-btn-primary">
          <ClockIcon />
          <span>Clock In<span className="hidden sm:inline">/Out</span></span>
        </Link>
      </Header>
      <main className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto">
          {/* Welcome Header */}
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold text-white mb-2">
              Welcome back, {user?.name?.split(' ')[0]}! 👋
            </h1>
            <p className="text-gray-400 text-lg">
              {new Date().toLocaleDateString('en-US', {
                weekday: 'long',
                month: 'long',
                day: 'numeric'
              })}
            </p>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
            <Link href="/shifts" className="sample-card bg-gradient-to-r from-blue-600 to-blue-700 text-white border-0 p-6 text-center">
              <CalendarIcon className="h-8 w-8 mx-auto mb-3" />
              <div className="font-semibold text-lg">My Shifts</div>
              <div className="text-blue-100 text-sm mt-1">View schedule</div>
            </Link>
            <Link href="/timesheet" className="sample-card bg-gradient-to-r from-green-600 to-green-700 text-white border-0 p-6 text-center">
              <ClockIcon className="h-8 w-8 mx-auto mb-3" />
              <div className="font-semibold text-lg">Clock In</div>
              <div className="text-green-100 text-sm mt-1">Start work</div>
            </Link>
          </div>

          {/* Upcoming Shifts */}
          <div className="sample-card-static">
            <div className="p-6">
              <h2 className="text-xl font-semibold text-white mb-6 pb-2 border-b border-gray-700">
                Upcoming Shifts
              </h2>
              {upcomingShifts.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {upcomingShifts.slice(0, 6).map((shift) => (
                    <ShiftCard
                      key={shift.id}
                      shift={convertShiftForCard(shift)}
                      onClick={handleShiftClick}
                    />
                  ))}
                </div>
              ) : (
                <div className="sample-empty-state">
                  <CalendarIcon className="mx-auto h-12 w-12 text-indigo-400" />
                  <h3 className="mt-4 text-xl font-semibold text-white">No Upcoming Shifts</h3>
                  <p className="text-gray-400 mt-2 max-w-md mx-auto">Check back later for new assignments or contact your supervisor.</p>
                </div>
              )}

              {upcomingShifts.length > 6 && (
                <div className="text-center mt-6 pt-4 border-t border-gray-700">
                  <Link
                    href="/shifts"
                    className="sample-btn-secondary"
                  >
                    View All {upcomingShifts.length} Shifts
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
