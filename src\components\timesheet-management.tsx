"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from '@/components/ui/button'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from '@/components/ui/badge'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { 
  AlertDialog, 
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle, 
  AlertDialogTrigger 
} from "@/components/ui/alert-dialog"
import { Clock, ClockIcon, FileText, Download, Users } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { AssignedPersonnel, RoleCode } from "@/lib/types";
import { format } from "date-fns"

interface TimesheetManagementProps {
  shiftId: string;
  assignedPersonnel: AssignedPersonnel[];
  onUpdate: () => void;
}

const ROLE_DEFINITIONS: Record<RoleCode, { name: string; color: string; bgColor: string; borderColor: string }> = {
  'CC': { name: 'Crew Chief', color: 'text-purple-700', bgColor: 'bg-purple-50', borderColor: 'border-purple-200' },
  'SH': { name: 'Stage Hand', color: 'text-blue-700', bgColor: 'bg-blue-50', borderColor: 'border-blue-200' },
  'FO': { name: 'Fork Operator', color: 'text-green-700', bgColor: 'bg-green-50', borderColor: 'border-green-200' },
  'RFO': { name: 'Reach Fork Operator', color: 'text-yellow-700', bgColor: 'bg-yellow-50', borderColor: 'border-yellow-200' },
  'RG': { name: 'Rigger', color: 'text-red-700', bgColor: 'bg-red-50', borderColor: 'border-red-200' },
  'GL': { name: 'General Labor', color: 'text-gray-700', bgColor: 'bg-gray-50', borderColor: 'border-gray-200' },
} as const

export default function TimesheetManagement({
  shiftId,
  assignedPersonnel,
  onUpdate
}: TimesheetManagementProps) {
  const { toast } = useToast()
  const [isProcessing, setIsProcessing] = useState(false)

  const handleClockAction = async (assignmentId: string, action: 'clock_in' | 'clock_out') => {
    setIsProcessing(true)
    try {
      const response = await fetch(`/api/shifts/${shiftId}/assigned/${assignmentId}/clock`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || `Failed to ${action.replace('_', ' ')}`)
      }

      const worker = assignedPersonnel.find(w => w.id === assignmentId)
      toast({
        title: action === 'clock_in' ? "Clocked In" : "Clocked Out",
        description: `${worker?.employee.name} has been ${action === 'clock_in' ? 'clocked in' : 'clocked out'} successfully`,
      })
      onUpdate()
    } catch (error) {
      console.error(`Error ${action}:`, error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : `Failed to ${action.replace('_', ' ')}`,
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const endWorkerShift = async (assignmentId: string, workerName: string) => {
    setIsProcessing(true)
    try {
      const response = await fetch(`/api/shifts/${shiftId}/assigned/${assignmentId}/end-shift`, {
        method: 'POST'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to end shift')
      }

      toast({
        title: "Shift Ended",
        description: `${workerName}'s shift has been ended`,
      })
      onUpdate()
    } catch (error) {
      console.error('Error ending shift:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to end shift",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const endAllShifts = async () => {
    const activeWorkers = assignedPersonnel.filter(w => w.status !== 'Shift Ended' && w.status !== 'shift_ended')
    if (activeWorkers.length === 0) {
      toast({
        title: "No Active Workers",
        description: "All workers have already ended their shifts",
      })
      return
    }

    setIsProcessing(true)
    try {
      const response = await fetch(`/api/shifts/${shiftId}/end-all-shifts`, {
        method: 'POST'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to end all shifts')
      }

      toast({
        title: "All Shifts Ended",
        description: `Ended shifts for ${activeWorkers.length} workers`,
      })
      onUpdate()
    } catch (error) {
      console.error('Error ending all shifts:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to end all shifts",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const finalizeTimesheet = async () => {
    const activeWorkers = assignedPersonnel.filter(w => w.status !== 'Shift Ended' && w.status !== 'shift_ended')
    if (activeWorkers.length > 0) {
      toast({
        title: "Cannot Finalize",
        description: `${activeWorkers.length} workers have not ended their shifts yet`,
        variant: "destructive",
      })
      return
    }

    setIsProcessing(true)
    try {
      const response = await fetch(`/api/shifts/${shiftId}/finalize-timesheet-simple`, {
        method: 'POST'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to finalize timesheet')
      }

      const result = await response.json()
      toast({
        title: "Timesheet Finalized",
        description: "Timesheet has been finalized and is pending client approval",
      })

      if (result.timesheetId) {
        window.open(`/timesheets/${result.timesheetId}/approve`, '_blank')
      }

      onUpdate()
    } catch (error) {
      console.error('Error finalizing timesheet:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to finalize timesheet",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const downloadTimesheet = async () => {
    try {
      const timesheetResponse = await fetch(`/api/timesheets?shiftId=${shiftId}`)
      if (!timesheetResponse.ok) {
        throw new Error('No timesheet found for this shift')
      }

      const timesheetData = await timesheetResponse.json()
      if (!timesheetData.timesheets || timesheetData.timesheets.length === 0) {
        toast({
          title: "No Timesheet",
          description: "Please finalize the timesheet first before downloading",
          variant: "destructive",
        })
        return
      }

      const timesheetId = timesheetData.timesheets[0].id

      const pdfResponse = await fetch(`/api/timesheets/${timesheetId}/pdf`)
      if (!pdfResponse.ok) {
        throw new Error('Failed to generate PDF')
      }

      const blob = await pdfResponse.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `timesheet-${shiftId}.pdf`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      toast({
        title: "PDF Downloaded",
        description: "Timesheet PDF has been downloaded successfully",
      })
    } catch (error) {
      console.error('Error downloading timesheet:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to download timesheet",
        variant: "destructive",
      })
    }
  }

  const getClockButtonText = (worker: AssignedPersonnel): string => {
    if (worker.status === 'Shift Ended' || worker.status === 'shift_ended') return 'Shift Ended'
    if (worker.status === 'Clocked In') return 'Clock Out'
    return 'Clock In'
  }

  const getClockButtonAction = (worker: AssignedPersonnel): 'clock_in' | 'clock_out' | null => {
    if (worker.status === 'Shift Ended' || worker.status === 'shift_ended') return null
    if (worker.status === 'Clocked In') return 'clock_out'
    return 'clock_in'
  }

  const formatTime = (timestamp?: string): string => {
    if (!timestamp) return '-'
    return format(new Date(timestamp), 'HH:mm')
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ClockIcon className="h-5 w-5" />
          Timesheet Management
        </CardTitle>
        <CardDescription>
          Manage clock in/out times and track worker hours
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Employee</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Clock In 1</TableHead>
                <TableHead>Clock Out 1</TableHead>
                <TableHead>Clock In 2</TableHead>
                <TableHead>Clock Out 2</TableHead>
                <TableHead>Clock In 3</TableHead>
                <TableHead>Clock Out 3</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {assignedPersonnel.map((worker) => {
                const roleDef = ROLE_DEFINITIONS[worker.roleCode as RoleCode]
                const clockAction = getClockButtonAction(worker)

                return (
                  <TableRow key={worker.id} className={`${roleDef?.bgColor || 'bg-gray-50'}`}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={worker.employee.avatar} />
                          <AvatarFallback>
                            {worker.employee.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{worker.employee.name}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className={`${roleDef?.color || 'text-gray-700'}`}>
                        {worker.roleCode}
                      </Badge>
                      <div className="text-sm text-muted-foreground">{worker.roleOnShift}</div>
                    </TableCell>

                    {[1, 2, 3].map((entryNum) => {
                      const entry = worker.timeEntries.find(e => e.entryNumber === entryNum)
                      return (
                        <React.Fragment key={entryNum}>
                          <TableCell className="text-center">
                            {formatTime(entry?.clockIn)}
                          </TableCell>
                          <TableCell className="text-center">
                            {formatTime(entry?.clockOut)}
                          </TableCell>
                        </React.Fragment>
                      )
                    })}

                    <TableCell>
                      <Badge
                        variant={(worker.status === 'Shift Ended' || worker.status === 'shift_ended') ? 'secondary' :
                               worker.status === 'Clocked In' ? 'default' : 'outline'}
                        className={
                          worker.status === 'Clocked In' ? 'bg-green-100 text-green-800' :
                          (worker.status === 'Shift Ended' || worker.status === 'shift_ended') ? 'bg-gray-100 text-gray-800' :
                          'bg-yellow-100 text-yellow-800'
                        }
                      >
                        {worker.status === 'shift_ended' ? 'Shift Ended' : worker.status}
                      </Badge>
                    </TableCell>

                    <TableCell>
                      <div className="flex items-center gap-2">
                        {clockAction && (
                          <Button
                            size="sm"
                            variant={clockAction === 'clock_in' ? 'default' : 'outline'}
                            onClick={() => handleClockAction(worker.id, clockAction)}
                            disabled={isProcessing}
                            className="min-w-[80px]"
                          >
                            <Clock className="h-3 w-3 mr-1" />
                            {getClockButtonText(worker)}
                          </Button>
                        )}

                        {worker.status !== 'Shift Ended' && worker.status !== 'shift_ended' && (
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button
                                size="sm"
                                variant="destructive"
                                disabled={isProcessing}
                              >
                                End Shift
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>End Shift</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to end {worker.employee.name}s shift?
                                  This will clock them out if theyre currently clocked in and mark their status as Shift Ended.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => endWorkerShift(worker.id, worker.employee.name)}
                                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                >
                                  End Shift
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </div>

        <div className="flex items-center justify-between mt-6 pt-4 border-t">
          <div className="flex items-center gap-4">
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant="outline"
                  disabled={isProcessing || assignedPersonnel.filter(w => w.status !== 'Shift Ended' && w.status !== 'shift_ended').length === 0}
                >
                  <Users className="h-4 w-4 mr-2" />
                  End All Shifts
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>End All Shifts</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to end shifts for all active workers? This will:
                                  <ul className="list-disc list-inside mt-2 space-y-1">
                                    <li>Clock out all currently clocked-in workers</li>
                                    <li>Mark all worker statuses as Shift Ended</li>
                                  </ul>
                                  <div className="mt-3 p-3 bg-muted rounded">
                                    <strong>Affected workers:</strong>
                                    <ul className="mt-1">
                                      {assignedPersonnel
                                        .filter(w => w.status !== 'Shift Ended' && w.status !== 'shift_ended')
                                        .map(w => (
                                          <li key={w.id} className="text-sm">• {w.employee.name} ({w.roleOnShift})</li>
                                        ))}
                                    </ul>
                                  </div>
                                </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={endAllShifts}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    End All Shifts
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>

          <div className="flex items-center gap-4">
            <Button
              onClick={downloadTimesheet}
              variant="outline"
              disabled={isProcessing}
            >
              <Download className="h-4 w-4 mr-2" />
              Download PDF
            </Button>
            <Button
              onClick={finalizeTimesheet}
              disabled={isProcessing || assignedPersonnel.some(w => w.status !== 'Shift Ended' && w.status !== 'shift_ended')}
              className="bg-primary text-primary-foreground hover:bg-primary/90"
            >
              <FileText className="h-4 w-4 mr-2" />
              Finalize Timesheet
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}