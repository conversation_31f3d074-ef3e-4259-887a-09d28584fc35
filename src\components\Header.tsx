import React from 'react';
import { Briefcase } from 'lucide-react';

interface HeaderProps {
    children?: React.ReactNode;
}

const Header: React.FC<HeaderProps> = ({ children }) => {
  return (
    <header className="sample-header">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <div className="flex-shrink-0 text-indigo-400">
               <Briefcase className="h-8 w-8" />
            </div>
            <span className="ml-3 text-xl font-bold text-white">Hands On Labor</span>
          </div>
          {children && <div>{children}</div>}
        </div>
      </div>
    </header>
  );
};

export default Header;