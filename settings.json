{
    // --- General Editor & Files ---
    "editor.defaultFormatter": "esbenp.prettier-vscode", // Global default formatter
    "notebook.defaultFormatter": "esbenp.prettier-vscode", // Default formatter for notebooks
    "files.autoSave": true, // Auto-save files after a delay
    "explorer.confirmDelete": false, // Warning: No confirmation dialog when deleting files/folders

    // --- Git & Diff Editor ---
    "git.enableSmartCommit": true, // Enable smart commit (stage all changes if nothing is staged)
    "git.confirmSync": false, // Do not confirm before syncing Git repositories
    "diffEditor.codeLens": true, // Show CodeLens in diff editor
    "gitlens.ai.model": "gitkraken", // GitLens AI model setting
    "gitlens.ai.gitkraken.model": "gemini:gemini-2.0-flash", // Specific GitKraken AI model


  "extensions.experimental.affinity": {
    "better-ts-errors.better-ts-errors": 1
  }
,
    // --- Language-Specific Overrides (Prettier) ---
    // These are slightly redundant given the global "editor.defaultFormatter",
    // but don't cause issues and explicitly ensure Prettier for these types.
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
// put this line in settings.json
"typescript.tsserver.experimental.enableProjectDiagnostics": true
,
    // --- Python Language Server (Pylance) Analysis Settings ---
    "python.analysis.addHoverSummaries": true, // Show hover summaries for symbols
    "python.analysis.autoImportCompletions": true, // Enable auto-import completions
    "python.analysis.diagnosticMode": "workspace", // Apply diagnostics to the entire workspace
    "python.analysis.displayEnglishDiagnostics": true, // Display diagnostics in English
    "python.analysis.enableTroubleshootMissingImports": true, // Offer troubleshooting for missing imports
    "python.analysis.importFormat": "relative", // Prefer relative imports
    "python.analysis.inlayHints.callArgumentNames": "all", // Show inlay hints for call argument names
    "python.analysis.inlayHints.functionReturnTypes": true, // Show inlay hints for function return types
    "python.analysis.inlayHints.pytestParameters": true, // Show inlay hints for pytest parameters
    "python.analysis.inlayHints.variableTypes": true, // Show inlay hints for variable types
    "python.analysis.languageServerMode": "full", // Use full language server capabilities
    "python.analysis.logLevel": "Warning", // Changed from "Trace" to "Warning" for less verbose logging
    "python.analysis.nodeExecutable": "auto", // Automatically detect Node.js executable
    "python.analysis.supportAllPythonDocuments": true, // Enable analysis for all Python documents
    "python.analysis.userFileIndexingLimit": -1, // No limit on user file indexing (-1 means unlimited)
    "python.analysis.usePullDiagnostics": false, // Use push diagnostics instead of pull (can affect performance)

    // --- Integrated Terminal Settings ---
    "terminal.integrated.suggest.enabled": true, // Enable suggestions in the integrated terminal
    "terminal.integrated.allowedLinkSchemes": [ // Allowed link schemes for clickable links
        "file",
        "http",
        "https",
        "mailto",
        "vscode",
        "vscode-insiders",
        "docker-desktop"
    ],

    
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp"]
    }
  },

    "terminal.integrated.profiles.windows": { // Define terminal profiles for Windows
        "PowerShell": {
            "source": "PowerShell",
            "icon": "terminal-powershell"
        },
        "Command Prompt": {
            "path": [
                "${env:windir}\\Sysnative\\cmd.exe",
                "${env:windir}\\System32\\cmd.exe"
            ],
            "args": [],
            "icon": "terminal-cmd"
        },
        "Git Bash": {
            "source": "Git Bash"
        },
        "Ubuntu (WSL)": {
            "path": "C:\\WINDOWS\\System32\\wsl.exe",
            "args": [
                "-d",
                "Ubuntu-22.04"
            ]
        }
    },
    "terminal.integrated.defaultProfile.windows": "Ubuntu (WSL)", // Set default terminal profile for Windows
    "terminal.external.linuxExec": "wsl", // Use WSL for external Linux terminal commands
    "terminal.integrated.defaultProfile.linux": "", // Consider setting a default if you use VS Code on native Linux

    // --- Workbench & UI ---
    "workbench.editor.empty.hint": "hidden", // Hide hint for empty editor groups
    "workbench.commandPalette.experimental.askChatLocation": "quickChat", // Experimental chat location in command palette

    // --- Experimental Cloud Changes (Opt-Out) ---
    "workbench.experimental.cloudChanges.autoStore": "off",
    "workbench.cloudChanges.autoResume": "off",
    "workbench.cloudChanges.continueOn": "off",

    // --- Extension-Specific Settings (Valid if extensions are installed) ---
    "geminicodeassist.project": "elated-fabric-460119-t3", // Gemini Code Assist project setting
    "augment.chat.userGuidelines": "help with getting timecard / shift scheduling / employee management web app fully functional and all requested features implemented correctly. this needs to be done today", // Augment chat guidelines
    "augment.nextEdit.highlightSuggestionsInTheEditor": true, // Augment next edit suggestions
    "augment.advanced": { }, // Augment advanced settings (empty object is valid)
    "projectManager.git.baseFolders": [ "c:\\users\\<USER>\\holiiiii\\holitime"], // Project Manager Git base folders
    "cloudcode.project": "elated-fabric-460119-t3", // Cloud Code project setting
    "containers.commands.build": "${containerCommand} build --pull --rm -f \"${dockerfile}\" -t ${tag} \"${context}\"" // Docker/Containers build command
}