
import React, { useMemo, useState } from 'react';
import { Company, Job, JobStatus } from '../types';
import { BuildingOfficeIcon, SearchIcon, CheckCircleIcon, SparklesIcon } from './IconComponents';

interface ClientDirectoryPageProps {
  companies: Company[];
  jobs: Job[];
}

const ClientDirectoryPage: React.FC<ClientDirectoryPageProps> = ({ companies, jobs }) => {
  const [searchTerm, setSearchTerm] = useState('');

  const companyData = useMemo(() => {
    return companies
        .map(company => {
            const companyJobs = jobs.filter(job => job.companyId === company.id);
            const activeJobs = companyJobs.filter(job => job.status === JobStatus.Active).length;
            const completedJobs = companyJobs.filter(job => job.status === JobStatus.Completed).length;
            return { ...company, activeJobs, completedJobs, totalJobs: companyJobs.length };
        })
        .filter(company => company.name.toLowerCase().includes(searchTerm.toLowerCase()));
  }, [companies, jobs, searchTerm]);

  return (
    <div className="bg-gray-800/50 rounded-xl p-4 sm:p-6 lg:p-8 border border-gray-700">
        <div className="flex flex-col sm:flex-row justify-between sm:items-center mb-6 gap-4">
            <div className="flex items-center gap-3">
                <BuildingOfficeIcon className="h-8 w-8 text-indigo-400" />
                <div>
                    <h1 className="text-2xl font-bold text-white">Client Directory</h1>
                    <p className="text-gray-400">An overview of all client companies.</p>
                </div>
            </div>
            <div className="relative flex-grow max-w-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <SearchIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                type="text"
                placeholder="Search by company name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full bg-gray-700 border border-transparent rounded-md py-2.5 pl-10 pr-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                />
            </div>
        </div>
        
        {companyData.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {companyData.map(company => (
                <div key={company.id} className="group bg-gray-800 rounded-lg shadow-md border border-gray-700/50 p-6 flex flex-col transition-all duration-300 hover:border-indigo-500 hover:shadow-indigo-500/10 hover:-translate-y-1">
                    <div className="flex items-start justify-between">
                        <h2 className="text-xl font-bold text-white mb-4 pr-4">{company.name}</h2>
                    </div>
                    
                    <div className="flex-grow space-y-3 text-sm">
                        <div className="flex items-center text-gray-300">
                           <SparklesIcon className="h-5 w-5 text-green-400"/>
                           <span className="ml-3"><span className="font-bold text-white">{company.activeJobs}</span> Active Jobs</span>
                        </div>
                         <div className="flex items-center text-gray-300">
                           <CheckCircleIcon className="h-5 w-5 text-gray-400"/>
                           <span className="ml-3"><span className="font-bold text-white">{company.completedJobs}</span> Completed Jobs</span>
                        </div>
                    </div>

                    <div className="mt-6 pt-4 border-t border-gray-700">
                        <p className="text-xs text-gray-400">Total Jobs: {company.totalJobs}</p>
                    </div>
                </div>
            ))}
            </div>
        ) : (
             <div className="text-center py-16 text-gray-500">
                No companies found matching your search.
            </div>
        )}
    </div>
  );
};

export default ClientDirectoryPage;
