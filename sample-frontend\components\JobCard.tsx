
import React from 'react';
import { Job, JobStatus, ShiftStatus } from '../types';
import { UsersIcon, BuildingOfficeIcon, CalendarDaysIcon } from './IconComponents';

interface JobCardProps {
  job: Job;
  onClick: (job: Job) => void;
}

const JobCard: React.FC<JobCardProps> = ({ job, onClick }) => {
  const getStatusClasses = (status: JobStatus) => {
    switch (status) {
      case JobStatus.Active:
        return 'bg-green-500 text-green-50 ring-1 ring-inset ring-green-600/20';
      case JobStatus.Completed:
        return 'bg-gray-500 text-gray-100 ring-1 ring-inset ring-gray-500/20';
      case JobStatus.Pending:
        return 'bg-yellow-500 text-yellow-900 ring-1 ring-inset ring-yellow-600/20';
      case JobStatus.OnHold:
          return 'bg-red-500 text-white ring-1 ring-inset ring-red-600/20';
      default:
        return 'bg-gray-600 text-white ring-1 ring-inset ring-gray-500/20';
    }
  };

  const upcomingShifts = job.shifts.filter(s => s.status === ShiftStatus.Upcoming).length;
  const totalShifts = job.shifts.length;

  return (
    <div 
      onClick={() => onClick(job)}
      className="group bg-gray-800 rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-indigo-500/20 hover:ring-2 hover:ring-indigo-500 flex flex-col cursor-pointer hover:-translate-y-1"
      role="button"
      tabIndex={0}
      onKeyDown={(e) => (e.key === 'Enter' || e.key === ' ') && onClick(job)}
    >
      <div className="p-6 flex-grow flex flex-col">
        <div className="flex justify-between items-start">
          <h2 className="text-lg sm:text-xl font-bold text-white mb-2 leading-tight pr-2">{job.name}</h2>
          <span className={`flex-shrink-0 px-3 py-1 text-xs font-semibold rounded-full ${getStatusClasses(job.status)}`}>
            {job.status}
          </span>
        </div>
        
        <div className="flex items-center text-gray-400 mt-2 mb-4 transition-colors group-hover:text-gray-200">
            <BuildingOfficeIcon />
            <span className="ml-2 text-sm">{job.company.name}</span>
        </div>

        <p className="text-gray-300 text-sm mb-4 min-h-[60px] line-clamp-3 flex-grow">
            {job.description}
        </p>

        <div className="mt-auto pt-4 border-t border-gray-700 flex justify-between items-center text-sm text-gray-400">
            <div className="flex items-center transition-colors group-hover:text-indigo-300">
                <UsersIcon />
                <span className="ml-2">{job.requestedWorkers} workers needed</span>
            </div>
            <div className="flex items-center transition-colors group-hover:text-indigo-300" title={`${upcomingShifts} upcoming shifts out of ${totalShifts} total`}>
                <CalendarDaysIcon />
                <span className="ml-2 font-medium">
                  <span className="text-white group-hover:text-indigo-200">{upcomingShifts}</span> / {totalShifts} Shifts
                </span>
            </div>
        </div>
      </div>
    </div>
  );
};

export default JobCard;