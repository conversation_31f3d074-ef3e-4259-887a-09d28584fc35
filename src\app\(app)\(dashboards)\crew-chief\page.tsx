'use client';

import { useUser } from '@/hooks/use-user';
import { useApi } from '@/hooks/use-api';
import { useEffect, useState } from 'react';
import Header from '@/components/Header';
import ShiftCard from '@/components/ShiftCard';
import { PlusIcon, SparklesIcon } from '@/components/IconComponents';
import ShiftManager from '@/components/dashboard/shift-management/shift-manager';
import { ShiftWithDetails } from '@/components/dashboard/shift-management/types';
import { Shift, Job, Company } from '@prisma/client';

type ActiveShift = Shift & {
  job: Job & {
    company: Company;
  };
};

type DashboardData = {
  activeShifts: ActiveShift[];
};

// Convert Prisma shift to ShiftCard format
const convertShiftForCard = (shift: ActiveShift) => ({
  id: shift.id,
  jobName: shift.job.name,
  companyName: shift.job.company.name,
  date: shift.date.toISOString().split('T')[0],
  startTime: shift.startTime,
  endTime: shift.endTime,
  status: shift.status,
  assignments: [] // Crew chief dashboard doesn't need assignment details for card view
});

export default function CrewChiefDashboard() {
  const { user } = useUser();
  const [expandedShift, setExpandedShift] = useState<string | null>(null);

  const { data, loading, error, refetch } = useApi<DashboardData>(
    user?.id ? `/api/crew-chief/${user.id}/dashboard` : null
  );
  const { data: shiftDetails, loading: shiftDetailsLoading, error: shiftDetailsError, refetch: refetchShiftDetails } = useApi<ShiftWithDetails>(
    expandedShift ? `/api/shifts/${expandedShift}/details` : null
  );

  useEffect(() => {
    refetch();
  }, [refetch]);

  const handleToggleShift = (shiftId: string) => {
    if (expandedShift === shiftId) {
      setExpandedShift(null);
    } else {
      setExpandedShift(shiftId);
    }
  };

  const handleShiftClick = (shift: any) => {
    handleToggleShift(shift.id);
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div className="text-red-500">Error loading dashboard data.</div>;
  }

  const activeShifts = data?.activeShifts || [];

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100 font-sans">
      <Header>
        <button className="sample-btn-primary">
          <PlusIcon />
          <span>Manage<span className="hidden sm:inline"> Team</span></span>
        </button>
      </Header>
      <main className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto">
          {/* Welcome Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-white mb-2">Welcome, {user?.name}!</h1>
            <p className="text-gray-400">Crew Chief Dashboard - Manage your team and shifts</p>
          </div>

          {/* Active Shifts */}
          <div className="sample-card-static">
            <div className="p-6">
              <h2 className="text-xl font-semibold text-white mb-6 pb-2 border-b border-gray-700">
                My Active Shifts
              </h2>
              {activeShifts.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {activeShifts.map((shift) => (
                    <ShiftCard
                      key={shift.id}
                      shift={convertShiftForCard(shift)}
                      onClick={handleShiftClick}
                    />
                  ))}
                </div>
              ) : (
                <div className="sample-empty-state">
                  <SparklesIcon className="mx-auto h-12 w-12 text-indigo-400" />
                  <h3 className="mt-4 text-xl font-semibold text-white">No Active Shifts</h3>
                  <p className="text-gray-400 mt-2 max-w-md mx-auto">You don't have any active shifts assigned. Check back later or contact your manager.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>

      {/* Shift Details Modal */}
      {expandedShift && shiftDetails && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="sample-card-static max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold text-white">Shift Management</h2>
                <button
                  onClick={() => setExpandedShift(null)}
                  className="text-gray-400 hover:text-white"
                >
                  ×
                </button>
              </div>
              <ShiftManager
                shift={shiftDetails}
                onUpdate={() => {
                  refetch();
                  refetchShiftDetails();
                }}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
