'use client';

import React from 'react';
import { MantineProvider } from '@mantine/core';
import NextAuthSessionProvider from '@/components/providers/session-provider';
import { ThemeProvider } from '@/components/providers/theme-provider';

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <NextAuthSessionProvider>
      <MantineProvider>
        <ThemeProvider>{children}</ThemeProvider>
      </MantineProvider>
    </NextAuthSessionProvider>
  );
}
