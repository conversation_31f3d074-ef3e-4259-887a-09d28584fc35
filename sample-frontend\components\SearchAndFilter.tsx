
import React from 'react';
import { SearchIcon } from './IconComponents';
import { JobStatus } from '../types';


interface SearchAndFilterProps {
  currentFilter?: JobStatus | 'all';
  onFilterChange?: (status: JobStatus | 'all') => void;
  onSearchChange: (term: string) => void;
  isShiftView?: boolean;
}

const SearchAndFilter: React.FC<SearchAndFilterProps> = ({ currentFilter, onFilterChange, onSearchChange, isShiftView = false }) => {
  const filterButtons: { label: string; value: JobStatus | 'all' }[] = [
    { label: 'All', value: 'all' },
    { label: 'Active', value: JobStatus.Active },
    { label: 'Completed', value: JobStatus.Completed },
  ];
  
  const baseButtonClass = "px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-indigo-500";
  const activeButtonClass = "bg-indigo-600 text-white shadow";
  const inactiveButtonClass = "bg-gray-700 text-gray-300 hover:bg-gray-600";

  return (
    <div className="flex flex-col md:flex-row gap-4">
      <div className="relative flex-grow">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <SearchIcon className="h-5 w-5 text-gray-400" />
        </div>
        <input
          type="text"
          placeholder={isShiftView ? "Search by job or company..." : "Search by job name, company..."}
          onChange={(e) => onSearchChange(e.target.value)}
          className="block w-full bg-gray-700 border border-transparent rounded-md py-2.5 pl-10 pr-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
        />
      </div>
      {!isShiftView && onFilterChange && (
        <div className="flex-shrink-0 flex items-center justify-center gap-2">
          {filterButtons.map(({ label, value }) => (
            <button
              key={value}
              onClick={() => onFilterChange(value)}
              className={`${baseButtonClass} ${currentFilter === value ? activeButtonClass : inactiveButtonClass}`}
            >
              {label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default SearchAndFilter;
