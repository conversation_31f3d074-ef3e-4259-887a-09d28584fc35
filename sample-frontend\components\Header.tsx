
import React from 'react';
import { BriefcaseIcon } from './IconComponents';

interface HeaderProps {
    children?: React.ReactNode;
}

const Header: React.FC<HeaderProps> = ({ children }) => {
  return (
    <header className="bg-gray-800/50 backdrop-blur-sm shadow-lg sticky top-0 z-10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <div className="flex-shrink-0 text-indigo-400">
               <BriefcaseIcon />
            </div>
            <span className="ml-3 text-xl font-bold text-white">Hands On Labor</span>
          </div>
          {children && <div>{children}</div>}
        </div>
      </div>
    </header>
  );
};

export default Header;
