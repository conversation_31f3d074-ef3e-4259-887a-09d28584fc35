
import React, { useState, useEffect } from 'react';
import { SparklesIcon } from './IconComponents';

const loadingMessages = [
  "Analyzing requirements...",
  "Thinking creatively...",
  "Consulting with the digital muses...",
  "Crafting the perfect job description...",
  "Building your job post...",
  "Just a moment...",
];

const AILoadingState: React.FC = () => {
  const [message, setMessage] = useState(loadingMessages[0]);

  useEffect(() => {
    const interval = setInterval(() => {
      setMessage(prevMessage => {
        const currentIndex = loadingMessages.indexOf(prevMessage);
        const nextIndex = (currentIndex + 1) % loadingMessages.length;
        return loadingMessages[nextIndex];
      });
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex flex-col items-center justify-center p-8 sm:p-12 min-h-[350px]">
      <div className="relative">
        <SparklesIcon className="w-16 h-16 text-indigo-400" />
        <div className="absolute inset-0 rounded-full bg-indigo-500/20 animate-ping -z-10"></div>
      </div>
      <p className="mt-6 text-lg font-semibold text-white transition-opacity duration-300 text-center">
        {message}
      </p>
      <div className="w-full max-w-xs bg-gray-700 rounded-full h-2 mt-8 overflow-hidden">
        <div className="bg-indigo-500 h-2 rounded-full animate-loading-bar"></div>
      </div>

      <style>{`
        @keyframes loading-bar {
          0% {
            transform: translateX(-100%);
          }
          100% {
            transform: translateX(100%);
          }
        }
        .animate-loading-bar {
          animation: loading-bar 1.5s linear infinite;
        }
      `}</style>
    </div>
  );
};

export default AILoadingState;