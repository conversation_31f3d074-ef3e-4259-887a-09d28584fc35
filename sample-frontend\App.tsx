
import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { Job, Company, Shift, User, UserRole, WorkerRole, Assignment, JobStatus, ShiftStatus, TimeEntry } from './types';
import Header from './components/Header';
import JobCard from './components/JobCard';
import ShiftCard from './components/ShiftCard';
import GenerateJobModal from './components/GenerateJobModal';
import JobDetailModal from './components/JobDetailModal';
import ShiftDetailModal from './components/ShiftDetailModal';
import SearchAndFilter from './components/SearchAndFilter';
import ShiftFormModal from './components/ShiftFormModal';
import { PlusIcon, SparklesIcon, CalendarDaysIcon, BriefcaseIcon, UsersGroupIcon, BuildingOfficeIcon, ClockIcon, DocumentTextIcon } from './components/IconComponents';
import PlaceholderPage from './components/PlaceholderPage';
import UserManagementPage from './components/UserManagementPage';
import ClientDirectoryPage from './components/ClientDirectoryPage';
import TimesheetProcessingPage from './components/TimesheetProcessingPage';
import ImportExportPage from './components/ImportExportPage';
import DocsPage from './components/DocsPage';


// MOCK DATA SETUP
const mockCompanies: Company[] = [
  { id: 'com1', name: 'ConstructCo' },
  { id: 'com2', name: 'EventScapes' },
  { id: 'com3', name: 'LogiPro Solutions' },
];

const mockUsers: User[] = [
    { id: 'user1', name: 'Alice Johnson', email: '<EMAIL>', role: UserRole.CrewChief, certifications: [WorkerRole.CrewChief, WorkerRole.ForkOperator], avatarUrl: `https://api.dicebear.com/8.x/lorelei/svg?seed=alice` },
    { id: 'user2', name: 'Bob Williams', email: '<EMAIL>', role: UserRole.Employee, certifications: [WorkerRole.StageHand, WorkerRole.Rigger], avatarUrl: `https://api.dicebear.com/8.x/lorelei/svg?seed=bob` },
    { id: 'user3', name: 'Charlie Brown', email: '<EMAIL>', role: UserRole.Employee, certifications: [WorkerRole.GeneralLabor, WorkerRole.Rigger], avatarUrl: `https://api.dicebear.com/8.x/lorelei/svg?seed=charlie` },
    { id: 'user4', name: 'Diana Miller', email: '<EMAIL>', role: UserRole.CrewChief, certifications: [WorkerRole.CrewChief], avatarUrl: `https://api.dicebear.com/8.x/lorelei/svg?seed=diana` },
    { id: 'user5', name: 'Ethan Davis', email: '<EMAIL>', role: UserRole.Employee, certifications: [WorkerRole.ForkOperator, WorkerRole.GeneralLabor, WorkerRole.ReachForkOperator], avatarUrl: `https://api.dicebear.com/8.x/lorelei/svg?seed=ethan` },
    { id: 'user6', name: 'Fiona Garcia', email: '<EMAIL>', role: UserRole.Employee, certifications: [WorkerRole.StageHand, WorkerRole.GeneralLabor], avatarUrl: `https://api.dicebear.com/8.x/lorelei/svg?seed=fiona` },
    { id: 'user7', name: 'George Clark', email: '<EMAIL>', role: UserRole.Employee, certifications: [WorkerRole.ReachForkOperator, WorkerRole.ForkOperator], avatarUrl: `https://api.dicebear.com/8.x/lorelei/svg?seed=george` },
];

const initialJobs: Job[] = [
  {
    id: 'job1',
    name: 'Downtown Office Tower Construction',
    description: 'Assist in the construction of a new 40-story office building. General labor duties include site cleanup, material handling, and assisting senior tradespeople.',
    status: JobStatus.Active,
    companyId: 'com1',
    company: mockCompanies[0],
    requestedWorkers: 15,
    shifts: [],
  },
  {
    id: 'job2',
    name: 'Annual Music Festival Setup',
    description: 'Responsible for setting up stages, lighting rigs, and sound equipment for a 3-day outdoor music festival. Requires ability to work long hours and lift heavy equipment.',
    status: JobStatus.Active,
    companyId: 'com2',
    company: mockCompanies[1],
    requestedWorkers: 25,
    shifts: [],
  },
  {
    id: 'job3',
    name: 'Warehouse Inventory Management',
    description: 'Day-to-day operations in a large warehouse. Includes sorting, packing, and shipping products. Forklift operator eligibility is a plus.',
    status: JobStatus.Completed,
    companyId: 'com3',
    company: mockCompanies[2],
    requestedWorkers: 10,
    shifts: [],
  }
];

const getShiftStatus = (shift: Omit<Shift, 'status'>): ShiftStatus => {
    const now = new Date();
    const endTime = new Date(`${shift.date}T${shift.endTime}`);

    if (shift.assignments.some(a => a.timeEntries.length > 0 && !a.timeEntries[a.timeEntries.length - 1].clockOut)) {
        return ShiftStatus.InProgress;
    }

    if (now > endTime) {
        return ShiftStatus.Completed;
    }
    
    return ShiftStatus.Upcoming;
}


let initialShifts: Shift[] = [
    { 
        id: 'shift1', 
        jobId: 'job2', 
        job: initialJobs[1], 
        date: new Date(Date.now() + 86400000 * 2).toISOString().split('T')[0], // 2 days from now
        startTime: '08:00', 
        endTime: '17:00',
        assignments: [
            { id: 'assign1-1', role: WorkerRole.CrewChief, userId: 'user1', timeEntries: [] },
            { id: 'assign1-2', role: WorkerRole.StageHand, userId: 'user2', timeEntries: [] },
            { id: 'assign1-3', role: WorkerRole.StageHand, userId: null, timeEntries: [] },
            { id: 'assign1-4', role: WorkerRole.GeneralLabor, userId: 'user3', timeEntries: [] },
            { id: 'assign1-5', role: WorkerRole.GeneralLabor, userId: null, timeEntries: [] },
        ],
        status: ShiftStatus.Upcoming
    },
    { 
        id: 'shift2', 
        jobId: 'job1', 
        job: initialJobs[0], 
        date: new Date().toISOString().split('T')[0], // Today
        startTime: '07:00', 
        endTime: '16:00',
        assignments: [
            { id: 'assign2-1', role: WorkerRole.CrewChief, userId: null, timeEntries: [] },
            { id: 'assign2-2', role: WorkerRole.ForkOperator, userId: 'user5', timeEntries: [{ clockIn: new Date(Date.now() - 3600000).toISOString() }] },
            { id: 'assign2-3', role: WorkerRole.GeneralLabor, userId: null, timeEntries: [] },
            { id: 'assign2-4', role: WorkerRole.GeneralLabor, userId: 'user6', timeEntries: [] },
        ],
        status: ShiftStatus.InProgress
    },
    { 
        id: 'shift3', 
        jobId: 'job2', 
        job: initialJobs[1], 
        date: new Date(Date.now() - 86400000).toISOString().split('T')[0], // Yesterday
        startTime: '09:00', 
        endTime: '18:00',
        assignments: [
            { id: 'assign3-1', role: WorkerRole.CrewChief, userId: 'user4', timeEntries: [{ clockIn: '2024-08-16T09:01:00Z', clockOut: '2024-08-16T18:05:00Z' }], isFinalized: true },
            { id: 'assign3-2', role: WorkerRole.StageHand, userId: 'user6', timeEntries: [{ clockIn: '2024-08-16T09:05:00Z', clockOut: '2024-08-16T18:02:00Z' }], isFinalized: true },
            { id: 'assign3-3', role: WorkerRole.StageHand, userId: null, timeEntries: [] },
        ],
        status: ShiftStatus.Completed
    }
];

initialShifts = initialShifts.map(s => ({ ...s, status: getShiftStatus(s) }));


// Link shifts to jobs
initialJobs.forEach(job => {
    job.shifts = initialShifts.filter(shift => shift.jobId === job.id);
});


export default function App() {
  const [jobs, setJobs] = useState<Job[]>(initialJobs);
  const [shifts, setShifts] = useState<Shift[]>(initialShifts);
  const [users] = useState<User[]>(mockUsers);
  const [companies] = useState<Company[]>(mockCompanies);
  
  const [isGenerateModalOpen, setIsGenerateModalOpen] = useState(false);
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [selectedShift, setSelectedShift] = useState<Shift | null>(null);
  
  const [isShiftFormModalOpen, setIsShiftFormModalOpen] = useState(false);
  const [shiftToEdit, setShiftToEdit] = useState<Shift | null>(null);
  const [currentJobIdForNewShift, setCurrentJobIdForNewShift] = useState<string | null>(null);

  const [activeTab, setActiveTab] = useState<'jobs' | 'shifts' | 'users' | 'clients' | 'timesheets' | 'import-export' | 'docs'>('jobs');
  const [filterStatus, setFilterStatus] = useState<JobStatus | 'all'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  const updateShiftInState = useCallback((updatedShift: Shift) => {
    // Update shift in the main shifts list
    const newShifts = shifts.map(s => s.id === updatedShift.id ? updatedShift : s);
    setShifts(newShifts);

    // Update the shift within its parent job's shift array
    const newJobs = jobs.map(j => {
      if (j.id === updatedShift.jobId) {
        const updatedShiftsForJob = j.shifts.map(s => s.id === updatedShift.id ? updatedShift : s);
        return { ...j, shifts: updatedShiftsForJob };
      }
      return j;
    });
    setJobs(newJobs);

    // If the modal for this shift is open, update its state too
    if (selectedShift?.id === updatedShift.id) {
        setSelectedShift(updatedShift);
    }
  }, [shifts, jobs, selectedShift]);


  const handleJobGenerated = (newJobData: Omit<Job, 'id' | 'status' | 'company' | 'shifts'>) => {
    const newJob: Job = {
      ...newJobData,
      id: `job-${Date.now()}`,
      status: JobStatus.Active,
      company: mockCompanies.find(c => c.id === newJobData.companyId) || mockCompanies[0],
      shifts: [],
    };
    setJobs(prevJobs => [newJob, ...prevJobs]);
    setIsGenerateModalOpen(false);
  };
  
  const handleAssignmentUpdate = useCallback((shiftId: string, assignmentId: string, newUserId: string | null) => {
    const shift = shifts.find(s => s.id === shiftId);
    if (!shift) return;
    
    const updatedAssignments = shift.assignments.map(assignment =>
        assignment.id === assignmentId ? { ...assignment, userId: newUserId } : assignment
    );
    const updatedShift = { ...shift, assignments: updatedAssignments };
    updateShiftInState(updatedShift);
  }, [shifts, updateShiftInState]);
  
  const handleClockIn = useCallback((shiftId: string, assignmentId: string) => {
    const shift = shifts.find(s => s.id === shiftId);
    if (!shift) return;

    const updatedAssignments = shift.assignments.map(assignment => {
        if (assignment.id === assignmentId) {
          const newTimeEntries = [...assignment.timeEntries, { clockIn: new Date().toISOString() }];
          return { ...assignment, timeEntries: newTimeEntries };
        }
        return assignment;
    });
    
    let updatedShift = { ...shift, assignments: updatedAssignments };
    updatedShift.status = getShiftStatus(updatedShift);
    updateShiftInState(updatedShift);
  }, [shifts, updateShiftInState]);

  const handleClockOut = useCallback((shiftId: string, assignmentId: string) => {
      const shift = shifts.find(s => s.id === shiftId);
      if (!shift) return;
  
      const updatedAssignments = shift.assignments.map(assignment => {
          if (assignment.id === assignmentId) {
            const lastEntryIndex = assignment.timeEntries.length - 1;
            if (lastEntryIndex >= 0 && !assignment.timeEntries[lastEntryIndex].clockOut) {
                const updatedEntries = [...assignment.timeEntries];
                updatedEntries[lastEntryIndex] = { ...updatedEntries[lastEntryIndex], clockOut: new Date().toISOString() };
                return { ...assignment, timeEntries: updatedEntries };
            }
          }
          return assignment;
      });
      
      let updatedShift = { ...shift, assignments: updatedAssignments };
      updatedShift.status = getShiftStatus(updatedShift);
      updateShiftInState(updatedShift);
  }, [shifts, updateShiftInState]);
  
  const handleEndShift = useCallback((shiftId: string, assignmentId: string) => {
    const shift = shifts.find(s => s.id === shiftId);
    if (!shift) return;

    const updatedAssignments = shift.assignments.map(assignment => {
        if (assignment.id === assignmentId) {
            let finalEntries: TimeEntry[] = JSON.parse(JSON.stringify(assignment.timeEntries));
            const lastEntryIndex = finalEntries.length - 1;
            
            // If currently clocked in, clock out first.
            if (lastEntryIndex >= 0 && !finalEntries[lastEntryIndex].clockOut) {
                finalEntries[lastEntryIndex].clockOut = new Date().toISOString();
            }
            
            return { ...assignment, timeEntries: finalEntries, isFinalized: true };
        }
        return assignment;
    });

    let updatedShift = { ...shift, assignments: updatedAssignments };
    updatedShift.status = getShiftStatus(updatedShift);
    updateShiftInState(updatedShift);
  }, [shifts, updateShiftInState]);

  // Effect to periodically update shift statuses
  useEffect(() => {
    const interval = setInterval(() => {
      setShifts(currentShifts =>
        currentShifts.map(s => {
          const newStatus = getShiftStatus(s);
          if (s.status !== newStatus) {
            const updatedShift = { ...s, status: newStatus };
            // Also update in jobs array
            setJobs(prevJobs => prevJobs.map(j => j.id === updatedShift.jobId ? {...j, shifts: j.shifts.map(jobShift => jobShift.id === updatedShift.id ? updatedShift : jobShift)} : j));
            return updatedShift;
          }
          return s;
        })
      );
    }, 60000); // every minute

    return () => clearInterval(interval);
  }, []);

  const handleOpenShiftFormForCreate = (jobId: string) => {
      setCurrentJobIdForNewShift(jobId);
      setShiftToEdit(null);
      setIsShiftFormModalOpen(true);
  };

  const handleOpenShiftFormForEdit = (shift: Shift) => {
      setShiftToEdit(shift);
      setCurrentJobIdForNewShift(null);
      setIsShiftFormModalOpen(true);
  };

  const handleCloseShiftForm = () => {
      setIsShiftFormModalOpen(false);
      setShiftToEdit(null);
      setCurrentJobIdForNewShift(null);
  };
  
  const handleSaveShift = (shiftData: { date: string; startTime: string; endTime: string; roleCounts: Record<WorkerRole, number> }) => {
    // Editing existing shift
    if (shiftToEdit) {
      let updatedShift = {
          ...shiftToEdit,
          ...shiftData,
          assignments: reconcileAssignments(shiftToEdit.assignments, shiftData.roleCounts),
      };
      updatedShift.status = getShiftStatus(updatedShift);
      updateShiftInState(updatedShift);
      setSelectedShift(updatedShift);
    } 
    // Creating new shift
    else if (currentJobIdForNewShift) {
        const job = jobs.find(j => j.id === currentJobIdForNewShift);
        if (!job) return;

        let newShift: Omit<Shift, 'status'> & {status?: ShiftStatus} = {
            id: `shift-${Date.now()}`,
            jobId: job.id,
            job: job,
            ...shiftData,
            assignments: generateAssignmentsFromCounts(shiftData.roleCounts),
        };
        newShift.status = getShiftStatus(newShift);

        setShifts(prev => [...prev, newShift as Shift]);
        setJobs(prevJobs => prevJobs.map(j => j.id === job.id ? {...j, shifts: [...j.shifts, newShift as Shift]} : j));
    }
    handleCloseShiftForm();
  };

  const handleDeleteShift = (shiftId: string) => {
      const shiftToDelete = shifts.find(s => s.id === shiftId);
      if(!shiftToDelete) return;
      
      setShifts(shifts.filter(s => s.id !== shiftId));
      setJobs(prevJobs => prevJobs.map(j => j.id === shiftToDelete.jobId ? {...j, shifts: j.shifts.filter(s => s.id !== shiftId)} : j));

      if (selectedShift?.id === shiftId) {
        setSelectedShift(null);
      }
      handleCloseShiftForm(); // Also close form modal if it was open for this shift
  }

  const generateAssignmentsFromCounts = (roleCounts: Record<WorkerRole, number>): Assignment[] => {
    const assignments: Assignment[] = [];
    Object.entries(roleCounts).forEach(([role, count]) => {
        for (let i = 0; i < count; i++) {
            assignments.push({
                id: `assign-${role.replace(/\s+/g, '')}-${Date.now()}-${i}`,
                role: role as WorkerRole,
                userId: null,
                timeEntries: [],
            });
        }
    });
    return assignments;
  };

  const reconcileAssignments = (existing: Assignment[], newCounts: Record<WorkerRole, number>): Assignment[] => {
      let reconciled: Assignment[] = [];
      Object.entries(newCounts).forEach(([roleStr, count]) => {
          const role = roleStr as WorkerRole;
          const existingForRole = existing.filter(a => a.role === role);
          const newForRole: Assignment[] = [];
          for (let i = 0; i < count; i++) {
              if (i < existingForRole.length) {
                  // Keep existing assignment
                  newForRole.push(existingForRole[i]);
              } else {
                  // Add new empty assignment
                  newForRole.push({
                      id: `assign-${role.replace(/\s+/g, '')}-${Date.now()}-${i}`,
                      role: role,
                      userId: null,
                      timeEntries: [],
                  });
              }
          }
          reconciled = [...reconciled, ...newForRole];
      });
      return reconciled;
  };
  
  const handleViewShift = (shift: Shift) => {
    setSelectedShift(shift);
  }

  const handleViewShiftFromJob = (shift: Shift) => {
    setSelectedJob(null);
    // Use a timeout to allow the job detail modal to animate out
    setTimeout(() => {
        setSelectedShift(shift);
    }, 200);
  };


  const filteredJobs = useMemo(() => {
    return jobs
      .filter(job => {
        if (filterStatus === 'all') return true;
        return job.status === filterStatus;
      })
      .filter(job =>
        job.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.company.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
  }, [jobs, filterStatus, searchTerm]);
  
  const categorizedShifts = useMemo(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const todaysShifts: Shift[] = [];
    const upcomingShifts: Shift[] = [];
    const pastShifts: Shift[] = [];
    
    shifts
      .filter(shift =>
        shift.job.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        shift.job.company.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
      .forEach(shift => {
        const shiftDate = new Date(shift.date);
        shiftDate.setHours(0,0,0,0);
        
        if (shiftDate.getTime() === today.getTime()) {
            todaysShifts.push(shift);
        } else if (shiftDate > today) {
            upcomingShifts.push(shift);
        } else {
            pastShifts.push(shift);
        }
      });
      
      return { todaysShifts, upcomingShifts, pastShifts: pastShifts.sort((a,b) => new Date(b.date).getTime() - new Date(a.date).getTime()) };

  }, [shifts, searchTerm]);

  type TabName = 'jobs' | 'shifts' | 'users' | 'clients' | 'timesheets' | 'import-export' | 'docs';
  const TabButton: React.FC<{tabName: TabName, icon: React.ReactNode, children: React.ReactNode}> = ({ tabName, icon, children }) => (
    <button
      onClick={() => setActiveTab(tabName)}
      className={`px-3 py-2 text-sm sm:text-base font-medium rounded-t-lg transition-colors duration-200 flex items-center gap-2 ${
        activeTab === tabName
          ? 'border-b-2 border-indigo-500 text-white'
          : 'border-b-2 border-transparent text-gray-400 hover:text-white'
      }`}
    >
      {icon}
      <span className="hidden sm:inline">{children}</span>
    </button>
  );
  
  const renderShiftGroup = (title: string, shifts: Shift[]) => {
      if (shifts.length === 0) return null;
      return (
          <div className="mb-10">
              <h2 className="text-xl font-semibold text-white mb-4 pb-2 border-b border-gray-700">{title}</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {shifts.map(shift => (
                  <ShiftCard key={shift.id} shift={shift} onClick={() => handleViewShift(shift)} />
                ))}
              </div>
          </div>
      )
  }

  const renderContent = () => {
      switch(activeTab) {
          case 'jobs':
            return (
              <>
                <SearchAndFilter
                    currentFilter={filterStatus}
                    onFilterChange={setFilterStatus}
                    onSearchChange={setSearchTerm}
                    isShiftView={false}
                />
                {filteredJobs.length > 0 ? (
                  <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {filteredJobs.map(job => (
                      <JobCard key={job.id} job={job} onClick={() => setSelectedJob(job)} />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-16 mt-8 bg-gray-800/50 rounded-lg border border-gray-700">
                      <SparklesIcon className="mx-auto h-12 w-12 text-indigo-400" />
                      <h3 className="mt-4 text-xl font-semibold text-white">No Jobs Found</h3>
                      <p className="text-gray-400 mt-2 mb-6 max-w-md mx-auto">It looks a little empty here. Why not create your first job using the power of AI?</p>
                      <button
                          onClick={() => setIsGenerateModalOpen(true)}
                          className="flex items-center justify-center gap-2 mx-auto bg-indigo-600 hover:bg-indigo-500 text-white font-semibold py-2 px-5 rounded-lg shadow-md transition-transform transform hover:scale-105"
                      >
                          <PlusIcon />
                          <span>Generate Your First Job</span>
                      </button>
                  </div>
                )}
              </>
            );
          case 'shifts':
             const { todaysShifts, upcomingShifts, pastShifts } = categorizedShifts;
             const noShiftsFound = todaysShifts.length === 0 && upcomingShifts.length === 0 && pastShifts.length === 0;
             return (
                 <>
                    <SearchAndFilter
                        onSearchChange={setSearchTerm}
                        isShiftView={true}
                    />
                    <div className="mt-6">
                        {noShiftsFound ? (
                            <div className="text-center py-16 mt-8 bg-gray-800/50 rounded-lg border border-gray-700">
                                <CalendarDaysIcon className="mx-auto h-12 w-12 text-indigo-400" />
                                <h3 className="mt-4 text-xl font-semibold text-white">No Shifts Found</h3>
                                <p className="text-gray-400 mt-2 max-w-md mx-auto">No shifts match your current search. Try a different search term or create a new job to start adding shifts.</p>
                            </div>
                        ) : (
                            <>
                                {renderShiftGroup("Today's Shifts", todaysShifts)}
                                {renderShiftGroup("Upcoming Shifts", upcomingShifts)}
                                {renderShiftGroup("Past Shifts", pastShifts)}
                            </>
                        )}
                    </div>
                 </>
             );
          case 'users':
              return <UserManagementPage users={users} />;
          case 'clients':
              return <ClientDirectoryPage companies={companies} jobs={jobs} />;
          case 'timesheets':
              return <TimesheetProcessingPage shifts={shifts} users={users} />;
          case 'import-export':
              return <ImportExportPage jobs={jobs} shifts={shifts} users={users} />;
          case 'docs':
              return <DocsPage />;
          default:
              return null;
      }
  };


  return (
    <div className="min-h-screen bg-gray-900 text-gray-100 font-sans">
      <Header>
        <button
            onClick={() => setIsGenerateModalOpen(true)}
            className="flex items-center justify-center gap-2 bg-indigo-600 hover:bg-indigo-500 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition-transform transform hover:scale-105"
        >
            <PlusIcon />
            <span>Generate Job<span className="hidden sm:inline"> with AI</span></span>
        </button>
      </Header>
      <main className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto">

            <div className="border-b border-gray-700">
                <nav className="-mb-px flex flex-wrap" aria-label="Tabs">
                    <TabButton tabName="jobs" icon={<BriefcaseIcon className="h-5 w-5" />}>Jobs</TabButton>
                    <TabButton tabName="shifts" icon={<CalendarDaysIcon className="h-5 w-5" />}>Shifts</TabButton>
                    <TabButton tabName="users" icon={<UsersGroupIcon className="h-5 w-5" />}>Users</TabButton>
                    <TabButton tabName="clients" icon={<BuildingOfficeIcon className="h-5 w-5" />}>Clients</TabButton>
                    <TabButton tabName="timesheets" icon={<ClockIcon className="h-5 w-5" />}>Timesheets</TabButton>
                    <TabButton tabName="import-export" icon={<DocumentTextIcon className="h-5 w-5" />}>Import/Export</TabButton>
                    <TabButton tabName="docs" icon={<SparklesIcon className="h-5 w-5" />}>Docs</TabButton>
                </nav>
            </div>
            
            <div className="mt-6">
                {renderContent()}
            </div>
        </div>
      </main>

      {isGenerateModalOpen && (
        <GenerateJobModal
          onClose={() => setIsGenerateModalOpen(false)}
          onJobGenerated={handleJobGenerated}
          companies={companies}
        />
      )}

      {selectedJob && (
        <JobDetailModal
            job={selectedJob}
            users={users}
            onClose={() => setSelectedJob(null)}
            onAddShift={() => {
              handleOpenShiftFormForCreate(selectedJob.id);
              setSelectedJob(null);
            }}
            onViewShift={handleViewShiftFromJob}
        />
      )}

      {selectedShift && (
        <ShiftDetailModal
            shift={selectedShift}
            users={users}
            onClose={() => setSelectedShift(null)}
            onAssignmentUpdate={handleAssignmentUpdate}
            onEditShift={() => {
              handleOpenShiftFormForEdit(selectedShift);
              setSelectedShift(null);
            }}
            onClockIn={handleClockIn}
            onClockOut={handleClockOut}
            onEndShift={handleEndShift}
        />
      )}

      {isShiftFormModalOpen && (
        <ShiftFormModal
            isOpen={isShiftFormModalOpen}
            onClose={handleCloseShiftForm}
            onSave={handleSaveShift}
            onDelete={handleDeleteShift}
            shift={shiftToEdit}
            job={shiftToEdit?.job || jobs.find(j => j.id === currentJobIdForNewShift)}
        />
      )}
    </div>
  );
}
