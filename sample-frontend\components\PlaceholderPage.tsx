
import React from 'react';
import { WrenchScrewdriverIcon } from './IconComponents';

interface PlaceholderPageProps {
  title: string;
  description?: string;
}

const PlaceholderPage: React.FC<PlaceholderPageProps> = ({ 
  title, 
  description = "This section is currently under construction. Check back soon for new features!" 
}) => {
  return (
    <div className="flex flex-col items-center justify-center text-center p-8 bg-gray-800 rounded-lg min-h-[400px]">
      <WrenchScrewdriverIcon className="h-16 w-16 text-indigo-400 mb-6" />
      <h2 className="text-2xl sm:text-3xl font-bold text-white mb-3">
        {title}
      </h2>
      <p className="max-w-md text-gray-400">
        {description}
      </p>
    </div>
  );
};

export default PlaceholderPage;
