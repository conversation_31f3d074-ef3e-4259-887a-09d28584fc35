
import React from 'react';
import { Shift, WorkerRole, WORKER_ROLE_DETAILS, ShiftStatus } from '../types';
import { BuildingOfficeIcon, CalendarIcon, ClockIcon } from './IconComponents';

interface ShiftCardProps {
  shift: Shift;
  onClick: (shift: Shift) => void;
}

const ShiftCard: React.FC<ShiftCardProps> = ({ shift, onClick }) => {
  const totalSlots = shift.assignments.length;
  const filledSlots = shift.assignments.filter(a => a.userId !== null).length;
  const filledPercentage = totalSlots > 0 ? (filledSlots / totalSlots) * 100 : 0;

  // Group assignments by role to get counts
  const roleCounts = shift.assignments.reduce((acc, assignment) => {
    acc[assignment.role] = (acc[assignment.role] || 0) + 1;
    return acc;
  }, {} as Record<WorkerRole, number>);

  const getStatusClasses = (status: ShiftStatus) => {
    switch (status) {
        case ShiftStatus.InProgress:
            return 'bg-yellow-400/10 text-yellow-300 ring-1 ring-inset ring-yellow-400/30';
        case ShiftStatus.Completed:
            return 'bg-green-500/10 text-green-300 ring-1 ring-inset ring-green-500/30';
        case ShiftStatus.Upcoming:
        default:
            return 'bg-blue-500/10 text-blue-300 ring-1 ring-inset ring-blue-500/30';
    }
  };

  return (
    <div 
      onClick={() => onClick(shift)}
      className="group bg-gray-800 rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-indigo-500/20 hover:ring-2 hover:ring-indigo-500 flex flex-col cursor-pointer hover:-translate-y-1"
      role="button"
      tabIndex={0}
      onKeyDown={(e) => (e.key === 'Enter' || e.key === ' ') && onClick(shift)}
    >
      <div className="p-6 flex-grow flex flex-col">
        <div className="flex justify-between items-start mb-2">
            <h2 className="text-lg sm:text-xl font-bold text-white leading-tight pr-2">{shift.job.name}</h2>
            <span className={`flex-shrink-0 px-3 py-1 text-xs font-semibold rounded-full ${getStatusClasses(shift.status)}`}>
                {shift.status}
            </span>
        </div>
        
        <div className="flex items-center text-gray-400 mt-2 mb-4 transition-colors group-hover:text-gray-200">
            <BuildingOfficeIcon />
            <span className="ml-2 text-sm">{shift.job.company.name}</span>
        </div>

        <div className="space-y-2 text-gray-300 text-sm border-t border-b border-gray-700 py-3 my-3">
             <div className="flex items-center">
                <CalendarIcon className="h-5 w-5 text-indigo-400" />
                <span className="ml-2 font-medium">{new Date(shift.date).toLocaleDateString(undefined, { weekday: 'short', month: 'long', day: 'numeric' })}</span>
             </div>
             <div className="flex items-center">
                <ClockIcon className="h-5 w-5 text-indigo-400" />
                <span className="ml-2">{shift.startTime} - {shift.endTime}</span>
             </div>
        </div>
        
        <div className="flex-grow">
            <h3 className="text-xs uppercase font-bold text-gray-500 mb-2">Workers Needed</h3>
            <div className="flex flex-wrap gap-2">
                {Object.entries(roleCounts).map(([role, count]) => {
                    const roleDetails = WORKER_ROLE_DETAILS[role as WorkerRole];
                    if (!roleDetails) return null;
                    return (
                        <span key={role} className={`px-2 py-1 text-xs font-semibold text-white rounded-full ${roleDetails.colorClass}`}>
                            {count} {role}
                        </span>
                    );
                })}
            </div>
        </div>

         <div className="mt-6">
          <div className="flex justify-between mb-1 text-xs text-gray-400">
            <span>Filled Positions</span>
            <span>{filledSlots} of {totalSlots}</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-3.5">
              <div 
                className="bg-gradient-to-r from-green-400 to-green-600 h-3.5 rounded-full transition-all duration-500"
                style={{ width: `${filledPercentage}%` }}
              ></div>
          </div>
      </div>
      </div>
     
    </div>
  );
};

export default ShiftCard;