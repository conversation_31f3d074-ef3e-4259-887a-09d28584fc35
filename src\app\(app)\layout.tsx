import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import '../globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Hands On Labor AI Dashboard',
  description: 'Workforce management dashboard',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={`${inter.className} bg-gray-900 text-gray-100 min-h-screen`}>
        <div className="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900
          bg-[length:200%_200%] animate-gradient-shift">
          {children}
        </div>
      </body>
    </html>
  )
}
