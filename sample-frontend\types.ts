
export enum UserRole {
  Staff = 'Staff',
  Admin = 'Admin',
  CompanyUser = 'CompanyUser',
  CrewChief = 'CrewChief',
  Employee = 'Employee',
}

export enum WorkerRole {
  CrewChief = 'Crew Chief',
  StageHand = 'Stage Hand',
  ForkOperator = 'Fork Operator',
  ReachForkOperator = 'Reach Fork Operator',
  Rigger = 'Rigger',
  GeneralLabor = 'General Labor',
}

export enum JobStatus {
    Pending = 'Pending',
    Active = 'Active',
    Completed = 'Completed',
    OnHold = 'On Hold'
}

export enum ShiftStatus {
    Upcoming = 'Upcoming',
    InProgress = 'In Progress',
    Completed = 'Completed'
}

export const WORKER_ROLE_DETAILS: Record<WorkerRole, { name: string; abbreviation: string; colorClass: string }> = {
  [WorkerRole.CrewChief]: { name: 'Crew Chief', abbreviation: 'CC', colorClass: 'bg-purple-500' },
  [WorkerRole.StageHand]: { name: 'Stage Hand', abbreviation: 'SH', colorClass: 'bg-blue-500' },
  [WorkerRole.ForkOperator]: { name: 'Fork Operator', abbreviation: 'FO', colorClass: 'bg-green-500' },
  [WorkerRole.ReachForkOperator]: { name: 'Reach Fork Operator', abbreviation: 'RFO', colorClass: 'bg-yellow-500' },
  [WorkerRole.Rigger]: { name: 'Rigger', abbreviation: 'RG', colorClass: 'bg-red-500' },
  [WorkerRole.GeneralLabor]: { name: 'General Labor', abbreviation: 'GL', colorClass: 'bg-gray-500' },
};


export interface Company {
  id: string;
  name: string;
  address?: string;
  phone?: string;
  email?: string;
}

export interface User {
    id:string;
    name: string;
    email: string;
    role: UserRole;
    avatarUrl?: string;
    certifications: WorkerRole[];
}

export interface TimeEntry {
    clockIn: string;
    clockOut?: string;
}

export interface Assignment {
    id: string;
    role: WorkerRole;
    userId: string | null;
    timeEntries: TimeEntry[];
    isFinalized?: boolean;
}

export interface Shift {
    id: string;
    jobId: string;
    job: Job;
    date: string;
    startTime: string;
    endTime: string;
    assignments: Assignment[];
    status: ShiftStatus;
}

export interface Job {
  id: string;
  name: string;
  description: string;
  status: JobStatus;
  companyId: string;
  company: Company;
  requestedWorkers: number;
  shifts: Shift[];
}
