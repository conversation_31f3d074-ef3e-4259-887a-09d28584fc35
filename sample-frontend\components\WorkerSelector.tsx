import React, { useState, useRef, useEffect, useCallback } from 'react';
import { User } from '../types';
import Avatar from './Avatar';
import { ChevronDownIcon } from './IconComponents';

interface WorkerSelectorProps {
  users: User[];
  selectedUserId: string | null;
  onChange: (userId: string | null) => void;
  disabled?: boolean;
}

const WorkerSelector: React.FC<WorkerSelectorProps> = ({ users, selectedUserId, onChange, disabled = false }) => {
  const [isOpen, setIsOpen] = useState(false);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const selectedUser = users.find(u => u.id === selectedUserId) || null;

  const handleToggle = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  const handleSelect = (userId: string | null) => {
    onChange(userId);
    setIsOpen(false);
  };
  
  const handleClickOutside = useCallback((event: MouseEvent) => {
    if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
      setIsOpen(false);
    }
  }, []);
  
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      setIsOpen(false);
    }
  }, []);

  useEffect(() => {
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleKeyDown);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, handleClickOutside, handleKeyDown]);

  return (
    <div className="relative w-full" ref={wrapperRef}>
      <button
        type="button"
        onClick={handleToggle}
        disabled={disabled}
        className="relative w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm pl-3 pr-10 py-1 text-left cursor-default focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
        aria-haspopup="listbox"
        aria-expanded={isOpen}
      >
        <span className="flex items-center">
          <Avatar user={selectedUser} size="xs" />
          <span className="ml-3 block truncate text-white">
            {selectedUser ? selectedUser.name : <span className="text-gray-400">-- Unassigned --</span>}
          </span>
        </span>
        <span className="ml-3 absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
          <ChevronDownIcon className={`h-5 w-5 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </span>
      </button>

      {isOpen && (
        <div
          className="absolute z-10 mt-1 w-full bg-gray-800 shadow-lg max-h-56 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm"
          role="listbox"
        >
          {/* Unassigned Option */}
          <div
            onClick={() => handleSelect(null)}
            className="text-gray-300 cursor-default select-none relative py-2 pl-3 pr-9 hover:bg-indigo-600 hover:text-white"
            role="option"
            aria-selected={!selectedUserId}
          >
            <div className="flex items-center">
              <span className="h-6 w-6 rounded-full flex items-center justify-center bg-gray-600 text-xs">?</span>
              <span className="font-normal ml-3 block truncate">
                -- Unassigned --
              </span>
            </div>
          </div>

          {users.map(user => (
            <div
              key={user.id}
              onClick={() => handleSelect(user.id)}
              className="text-gray-100 cursor-default select-none relative py-2 pl-3 pr-9 hover:bg-indigo-600 hover:text-white"
              role="option"
              aria-selected={selectedUserId === user.id}
            >
              <div className="flex items-center">
                <Avatar user={user} size="xs" />
                <span className="font-normal ml-3 block truncate">
                  {user.name}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default WorkerSelector;