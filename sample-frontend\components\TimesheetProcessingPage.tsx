
import React, { useState, useMemo } from 'react';
import { Shift, User, TimeEntry } from '../types';
import Avatar from './Avatar';
import { ChevronDownIcon, ClockIcon } from './IconComponents';

interface TimesheetProcessingPageProps {
  shifts: Shift[];
  users: User[];
}

const TimesheetProcessingPage: React.FC<TimesheetProcessingPageProps> = ({ shifts, users }) => {
  const today = new Date();
  const oneWeekAgo = new Date(today);
  oneWeekAgo.setDate(today.getDate() - 7);
  
  const [startDate, setStartDate] = useState(oneWeekAgo.toISOString().split('T')[0]);
  const [endDate, setEndDate] = useState(today.toISOString().split('T')[0]);
  const [openUserId, setOpenUserId] = useState<string | null>(null);

  const workerTimesheetData = useMemo(() => {
    const start = new Date(startDate).getTime();
    const end = new Date(endDate);
    end.setHours(23, 59, 59, 999); // Include the whole end day
    const endTime = end.getTime();

    const timesheets: Record<string, { user: User; entries: any[]; totalHours: number }> = {};

    shifts.forEach(shift => {
      const shiftDate = new Date(shift.date).getTime();
      if (shiftDate >= start && shiftDate <= endTime) {
        shift.assignments.forEach(assignment => {
          if (assignment.userId && assignment.timeEntries.length > 0) {
            if (!timesheets[assignment.userId]) {
              const user = users.find(u => u.id === assignment.userId);
              if (user) {
                timesheets[assignment.userId] = { user, entries: [], totalHours: 0 };
              }
            }

            if (timesheets[assignment.userId]) {
              assignment.timeEntries.forEach(entry => {
                if(entry.clockIn && entry.clockOut) {
                    const durationMs = new Date(entry.clockOut).getTime() - new Date(entry.clockIn).getTime();
                    const durationHours = durationMs / (1000 * 60 * 60);
                    
                    timesheets[assignment.userId].entries.push({
                        ...entry,
                        shiftDate: shift.date,
                        jobName: shift.job.name,
                        duration: durationHours.toFixed(2),
                    });
                    timesheets[assignment.userId].totalHours += durationHours;
                }
              });
            }
          }
        });
      }
    });

    return Object.values(timesheets).sort((a,b) => a.user.name.localeCompare(b.user.name));
  }, [shifts, users, startDate, endDate]);

  const toggleUser = (userId: string) => {
    setOpenUserId(openUserId === userId ? null : userId);
  };

  const formatTime = (isoString?: string) => new Date(isoString || '').toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

  return (
    <div className="bg-gray-800/50 rounded-xl p-4 sm:p-6 lg:p-8 border border-gray-700">
      <div className="flex flex-col sm:flex-row justify-between sm:items-center mb-6 gap-4">
        <div className="flex items-center gap-3">
            <ClockIcon className="h-8 w-8 text-indigo-400" />
            <div>
                <h1 className="text-2xl font-bold text-white">Timesheet Processing</h1>
                <p className="text-gray-400">Review and process worker hours.</p>
            </div>
        </div>
        <div className="flex flex-col sm:flex-row items-center gap-2 text-sm">
          <label htmlFor="startDate" className="text-gray-300">From:</label>
          <input type="date" id="startDate" value={startDate} onChange={e => setStartDate(e.target.value)} className="bg-gray-700 border border-gray-600 rounded-md py-1 px-2 text-white focus:ring-indigo-500 focus:border-indigo-500" />
          <label htmlFor="endDate" className="text-gray-300 mt-2 sm:mt-0 sm:ml-2">To:</label>
          <input type="date" id="endDate" value={endDate} onChange={e => setEndDate(e.target.value)} className="bg-gray-700 border border-gray-600 rounded-md py-1 px-2 text-white focus:ring-indigo-500 focus:border-indigo-500" />
        </div>
      </div>

      <div className="space-y-2">
        {workerTimesheetData.map(({ user, entries, totalHours }) => (
          <div key={user.id} className="bg-gray-800 rounded-lg overflow-hidden">
            <button onClick={() => toggleUser(user.id)} className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-700/50 transition-colors">
              <div className="flex items-center gap-4">
                <Avatar user={user} size="md" />
                <div>
                    <div className="font-bold text-lg text-white">{user.name}</div>
                    <div className="text-sm text-gray-400">{user.email}</div>
                </div>
              </div>
              <div className="flex items-center gap-6">
                <div className="text-right">
                    <div className="text-xl font-bold text-white">{totalHours.toFixed(2)}</div>
                    <div className="text-xs text-gray-400">Total Hours</div>
                </div>
                <ChevronDownIcon className={`h-6 w-6 text-gray-400 transition-transform ${openUserId === user.id ? 'rotate-180' : ''}`} />
              </div>
            </button>

            {openUserId === user.id && (
              <div className="p-4 bg-gray-900/70">
                {entries.length > 0 ? (
                    <div className="overflow-x-auto">
                        <table className="min-w-full text-sm">
                        <thead className="border-b border-gray-700">
                            <tr>
                                <th className="py-2 text-left font-semibold text-gray-300">Job</th>
                                <th className="py-2 text-left font-semibold text-gray-300">Date</th>
                                <th className="py-2 text-left font-semibold text-gray-300">Clock In</th>
                                <th className="py-2 text-left font-semibold text-gray-300">Clock Out</th>
                                <th className="py-2 text-right font-semibold text-gray-300">Duration (hrs)</th>
                            </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-700/50">
                            {entries.sort((a,b) => new Date(b.shiftDate).getTime() - new Date(a.shiftDate).getTime()).map((entry, index) => (
                                <tr key={index}>
                                    <td className="py-2 text-gray-200">{entry.jobName}</td>
                                    <td className="py-2 text-gray-200">{new Date(entry.shiftDate).toLocaleDateString()}</td>
                                    <td className="py-2 text-gray-200">{formatTime(entry.clockIn)}</td>
                                    <td className="py-2 text-gray-200">{formatTime(entry.clockOut)}</td>
                                    <td className="py-2 text-right text-gray-200 font-mono">{entry.duration}</td>
                                </tr>
                            ))}
                        </tbody>
                        </table>
                    </div>
                ) : (
                    <p className="text-center text-gray-500 py-4">No completed time entries for this user in the selected date range.</p>
                )}
              </div>
            )}
          </div>
        ))}
         {workerTimesheetData.length === 0 && (
            <div className="text-center py-16 text-gray-500">
                No timesheet data available for the selected date range.
            </div>
        )}
      </div>
    </div>
  );
};

export default TimesheetProcessingPage;
