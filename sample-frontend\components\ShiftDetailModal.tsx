
import React, { useEffect, useCallback, useState, useMemo } from 'react';
import { Shift, User, Assignment, WORKER_ROLE_DETAILS, ShiftStatus, TimeEntry } from '../types';
import { XIcon, CalendarIcon, ClockIcon, BuildingOfficeIcon, PencilIcon, ClockInIcon, ClockOutIcon, CheckCircleIcon } from './IconComponents';
import WorkerSelector from './WorkerSelector';

interface ShiftDetailModalProps {
  shift: Shift;
  users: User[];
  onClose: () => void;
  onAssignmentUpdate: (shiftId: string, assignmentId: string, userId: string | null) => void;
  onEditShift: () => void;
  onClockIn: (shiftId: string, assignmentId: string) => void;
  onClockOut: (shiftId: string, assignmentId: string) => void;
  onEndShift: (shiftId: string, assignmentId: string) => void;
}

const ShiftDetailModal: React.FC<ShiftDetailModalProps> = ({ shift, users, onClose, onAssignmentUpdate, onEditShift, onClockIn, onClockOut, onEndShift }) => {
  const [isShowing, setIsShowing] = useState(false);

  const maxTimeEntries = useMemo(() => {
    if (!shift.assignments || shift.assignments.length === 0) {
      return 1; // Default to at least one pair of columns
    }
    return shift.assignments.reduce((max, assignment) => {
        return Math.max(max, assignment.timeEntries.length);
    }, 0) || 1; // Ensure at least 1 column pair even if no entries
  }, [shift.assignments]);

  const handleClose = () => {
    setIsShowing(false);
    setTimeout(onClose, 200);
  }

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      handleClose();
    }
  }, [onClose]);

  useEffect(() => {
    setIsShowing(true);
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);
  
  const getStatusClasses = (status: ShiftStatus) => {
    switch (status) {
        case ShiftStatus.InProgress:
            return 'bg-yellow-400/10 text-yellow-300 ring-1 ring-inset ring-yellow-400/30';
        case ShiftStatus.Completed:
            return 'bg-green-500/10 text-green-300 ring-1 ring-inset ring-green-500/30';
        case ShiftStatus.Upcoming:
        default:
            return 'bg-blue-500/10 text-blue-300 ring-1 ring-inset ring-blue-500/30';
    }
  };

  const handleSelectChange = (assignmentId: string, newUserId: string | null) => {
    onAssignmentUpdate(shift.id, assignmentId, newUserId);
  };

  const formatTime = (isoString?: string) => {
    if (!isoString) return '--:--';
    return new Date(isoString).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  return (
    <div
      role="dialog"
      aria-modal="true"
      aria-labelledby="shift-detail-title"
      className={`fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 transition-opacity duration-200 ${isShowing ? 'opacity-100' : 'opacity-0'}`}
      onClick={handleClose}
    >
      <div
        className={`relative w-[90vw] max-w-6xl max-h-[90vh] bg-gray-900 rounded-xl shadow-2xl transform transition-all duration-200 flex flex-col ${isShowing ? 'scale-100 opacity-100' : 'scale-95 opacity-0'}`}
        onClick={e => e.stopPropagation()}
      >
        <div className="flex-shrink-0 p-6 sm:p-8 bg-gray-800/50 rounded-t-xl">
            <button
                onClick={handleClose}
                className="absolute top-4 right-4 text-gray-400 hover:text-white z-10"
                aria-label="Close modal"
            >
                <XIcon />
            </button>
            
            <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-2">
                <h2 id="shift-detail-title" className="text-2xl font-bold text-white pr-8">
                    {shift.job.name}
                </h2>
                <span className={`flex-shrink-0 px-3 py-1 text-sm font-semibold rounded-full ${getStatusClasses(shift.status)}`}>
                    {shift.status}
                </span>
            </div>
            <div className="flex items-center text-gray-400 text-sm mb-4">
                <BuildingOfficeIcon className="h-4 w-4"/>
                <span className="ml-2">{shift.job.company.name}</span>
            </div>

            <div className="flex flex-wrap gap-x-6 gap-y-2 text-gray-300 border-b border-t border-gray-700 py-4">
                <div className="flex items-center">
                    <CalendarIcon className="h-5 w-5 text-indigo-400" />
                    <span className="ml-2 text-base">{new Date(shift.date).toLocaleDateString(undefined, { weekday: 'long', month: 'long', day: 'numeric' })}</span>
                </div>
                <div className="flex items-center">
                    <ClockIcon className="h-5 w-5 text-indigo-400" />
                    <span className="ml-2 text-base">{shift.startTime} - {shift.endTime}</span>
                </div>
            </div>
        </div>

        <div className="overflow-y-auto px-2 sm:px-8 py-6 flex-grow">
          <div className="flex justify-between items-center mb-4 px-4 sm:px-0">
            <h3 className="text-lg font-semibold text-white">Worker Assignments & Time Tracking</h3>
            <button
                onClick={onEditShift}
                className="flex items-center gap-2 text-sm bg-gray-700 hover:bg-gray-600 text-white font-semibold py-2 px-3 rounded-md shadow-sm transition-colors"
            >
                <PencilIcon className="h-4 w-4" />
                <span className="hidden sm:inline">Edit Shift</span>
            </button>
          </div>
          <div className="overflow-x-auto relative shadow-md rounded-lg">
            <table className="min-w-full text-sm">
              <thead className="bg-gray-800">
                <tr>
                  <th scope="col" className="sticky left-0 z-20 bg-gray-800 px-4 py-3 text-left text-xs font-bold text-gray-400 uppercase tracking-wider">
                    Worker
                  </th>
                  {Array.from({ length: maxTimeEntries }, (_, i) => (
                    <React.Fragment key={`header-${i}`}>
                        <th scope="col" className="px-3 py-3 text-center text-xs font-bold text-gray-400 uppercase tracking-wider w-24">
                            In {i + 1}
                        </th>
                        <th scope="col" className="px-3 py-3 text-center text-xs font-bold text-gray-400 uppercase tracking-wider w-24">
                            Out {i + 1}
                        </th>
                    </React.Fragment>
                  ))}
                  <th scope="col" className="px-4 py-3 text-left text-xs font-bold text-gray-400 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-4 py-3 text-right text-xs font-bold text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-700">
                {shift.assignments.map((assignment) => {
                  const roleDetails = WORKER_ROLE_DETAILS[assignment.role];
                  const assignedUser = users.find(u => u.id === assignment.userId);
                  
                  const isStarted = assignment.timeEntries && assignment.timeEntries.length > 0;
                  const lastEntry = isStarted ? assignment.timeEntries[assignment.timeEntries.length - 1] : null;
                  const isClockedIn = isStarted && !!lastEntry && !lastEntry.clockOut;
                  const isFinalized = !!assignment.isFinalized;

                  return (
                    <tr key={assignment.id} className="group bg-gray-900 hover:bg-gray-800/60">
                        {/* Sticky Worker Column */}
                        <td className="sticky left-0 z-10 bg-gray-900 group-hover:bg-gray-800/60 px-4 py-2 whitespace-nowrap">
                            <div className="flex items-center gap-3 w-64">
                                <span title={assignment.role} className={`flex-shrink-0 p-1 w-8 h-8 flex items-center justify-center text-xs font-bold text-white rounded-full ${roleDetails.colorClass}`}>
                                    {roleDetails.abbreviation}
                                </span>
                                <div className="flex-grow">
                                    <WorkerSelector
                                      users={users.filter(user => user.certifications.includes(assignment.role))}
                                      selectedUserId={assignment.userId}
                                      onChange={(newUserId) => handleSelectChange(assignment.id, newUserId)}
                                      disabled={isClockedIn || isFinalized}
                                    />
                                </div>
                            </div>
                        </td>

                        {/* Dynamic Time Log Columns */}
                        {Array.from({ length: maxTimeEntries }, (_, i) => {
                            const entry = assignment.timeEntries[i];
                            return (
                                <React.Fragment key={`entry-${assignment.id}-${i}`}>
                                    <td className="px-3 py-2 whitespace-nowrap text-center">
                                        <span className="text-white font-mono">{formatTime(entry?.clockIn)}</span>
                                    </td>
                                    <td className="px-3 py-2 whitespace-nowrap text-center">
                                        <span className="text-white font-mono">{formatTime(entry?.clockOut)}</span>
                                    </td>
                                </React.Fragment>
                            )
                        })}
                        
                        {/* Status Column */}
                        <td className="px-4 py-2 whitespace-nowrap align-middle">
                           <div className="flex items-center justify-start gap-2">
                            {assignedUser ? (
                                isFinalized ? (
                                    <div className="flex items-center gap-2 text-green-400 font-medium">
                                        <CheckCircleIcon />
                                        <span>Completed</span>
                                    </div>
                                ) : isClockedIn ? (
                                    <div className="flex items-center gap-2 text-yellow-400 font-medium">
                                        <div className="relative flex h-3 w-3">
                                            <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-yellow-400 opacity-75"></span>
                                            <span className="relative inline-flex rounded-full h-3 w-3 bg-yellow-500"></span>
                                        </div>
                                        <span>Clocked In</span>
                                    </div>
                                ) : isStarted ? (
                                  <span className="text-gray-400">On Break</span>
                                ) : (
                                    <span className="text-gray-400 italic">Ready to Start</span>
                                )
                            ) : (
                                <span className="text-gray-500 italic">Unassigned</span>
                            )}
                          </div>
                        </td>

                        {/* Actions Column */}
                        <td className="px-4 py-2 whitespace-nowrap text-right align-middle">
                             <div className="flex flex-row items-center justify-end gap-2">
                             {assignedUser && !isFinalized ? (
                                <>
                                  {isClockedIn ? (
                                    <button onClick={() => onClockOut(shift.id, assignment.id)} className="flex items-center gap-2 bg-yellow-600 hover:bg-yellow-700 text-white font-semibold py-1 px-3 rounded-md transition-colors text-sm justify-center min-w-[110px]">
                                        <ClockOutIcon className="h-4 w-4" /> Clock Out
                                    </button>
                                  ) : (
                                    <button onClick={() => onClockIn(shift.id, assignment.id)} className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white font-semibold py-1 px-3 rounded-md transition-colors text-sm justify-center min-w-[110px]">
                                        <ClockInIcon className="h-4 w-4" /> {isStarted ? 'Clock In' : 'Start Shift'}
                                    </button>
                                  )}

                                  {isStarted && (
                                    <button onClick={() => onEndShift(shift.id, assignment.id)} className="flex items-center gap-2 bg-red-600 hover:bg-red-700 text-white font-semibold py-1 px-3 rounded-md transition-colors text-sm justify-center min-w-[110px]">
                                      End Shift
                                    </button>
                                  )}
                                </>
                             ) : (
                               <div className="h-8"></div>
                             )}
                            </div>
                        </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        </div>

        <div className="mt-auto p-6 pt-4 border-t border-gray-700 flex-shrink-0 flex justify-end bg-gray-800/50 rounded-b-xl">
            <button
                type="button"
                onClick={handleClose}
                className="w-full sm:w-auto inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-white hover:bg-gray-600 sm:text-sm"
            >
                Done
            </button>
        </div>
      </div>
    </div>
  );
};

export default ShiftDetailModal;
