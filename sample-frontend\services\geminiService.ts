
import { GoogleGenAI, Type } from "@google/genai";
import { JobStatus } from "../types";

if (!process.env.API_KEY) {
    throw new Error("API_KEY environment variable not set");
}

const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });

const jobSchema = {
  type: Type.OBJECT,
  properties: {
    name: {
      type: Type.STRING,
      description: "A concise and descriptive name for the job.",
    },
    description: {
      type: Type.STRING,
      description: "A detailed description of the job, including responsibilities, requirements, and any special conditions. Should be at least 2-3 sentences.",
    },
    requestedWorkers: {
      type: Type.INTEGER,
      description: "The estimated number of workers required for this job.",
    },
  },
  required: ["name", "description", "requestedWorkers"],
};


export const generateJobWithAI = async (prompt: string, companyName: string) => {
  try {
    const fullPrompt = `You are a workforce management assistant. Based on the following requirements, generate a job posting for the company "${companyName}". 
    
    Requirements: "${prompt}"
    
    Generate a concise job name, a detailed job description, and a recommended number of workers.
    `;
    
    const response = await ai.models.generateContent({
      model: "gemini-2.5-flash",
      contents: fullPrompt,
      config: {
        responseMimeType: "application/json",
        responseSchema: jobSchema,
      },
    });

    const text = response.text.trim();
    const generatedData = JSON.parse(text);
    
    return {
        name: generatedData.name,
        description: generatedData.description,
        requestedWorkers: generatedData.requestedWorkers,
        companyId: '' // Placeholder, will be handled in App.tsx
    };

  } catch (error) {
    console.error("Error generating job with AI:", error);
    if (error instanceof Error) {
        return { error: `Failed to generate job. Reason: ${error.message}` };
    }
    return { error: "An unknown error occurred while communicating with the AI." };
  }
};
