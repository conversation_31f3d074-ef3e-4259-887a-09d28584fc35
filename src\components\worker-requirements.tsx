"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from '@/components/ui/button'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from '@/components/ui/badge'

import { Users, Plus, Minus } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { RoleCode } from "@/lib/types";

interface WorkerRequirement {
  roleCode: RoleCode;
  requiredCount: number;
}

interface WorkerRequirementsProps {
  shiftId: string;
  workerRequirements: WorkerRequirement[];
  onUpdate: (updatedRequirements: WorkerRequirement[]) => void;
}

const ROLE_DEFINITIONS: Record<RoleCode, { name: string; color: string; bgColor: string; borderColor: string }> = {
  'CC': { name: 'Crew Chief', color: 'text-purple-700', bgColor: 'bg-purple-50', borderColor: 'border-purple-200' },
  'SH': { name: 'Stage Hand', color: 'text-blue-700', bgColor: 'bg-blue-50', borderColor: 'border-blue-200' },
  'FO': { name: 'Fork Operator', color: 'text-green-700', bgColor: 'bg-green-50', borderColor: 'border-green-200' },
  'RFO': { name: 'Reach Fork Operator', color: 'text-yellow-700', bgColor: 'bg-yellow-50', borderColor: 'border-yellow-200' },
  'RG': { name: 'Rigger', color: 'text-red-700', bgColor: 'bg-red-50', borderColor: 'border-red-200' },
  'GL': { name: 'General Labor', color: 'text-gray-700', bgColor: 'bg-gray-50', borderColor: 'border-gray-200' },
} as const

export default function WorkerRequirements({ shiftId, workerRequirements, onUpdate }: WorkerRequirementsProps) {
  const { toast } = useToast()
  const [isUpdating, setIsUpdating] = useState(false)

  const updateWorkerRequirement = async (roleCode: RoleCode, newCount: number) => {
    if (isUpdating || newCount < 0) return
    setIsUpdating(true)

    try {
      const allRoleTypes: RoleCode[] = ['CC', 'SH', 'FO', 'RFO', 'RG', 'GL']
      const updatedRequirements: WorkerRequirement[] = allRoleTypes.map(role => {
        if (role === roleCode) {
          return { roleCode: role, requiredCount: newCount }
        }
        const existing = workerRequirements.find(req => req.roleCode === role)
        return existing || { roleCode: role, requiredCount: 0 }
      })

      const response = await fetch(`/api/shifts/${shiftId}/worker-requirements`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ workerRequirements: updatedRequirements })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update worker requirements')
      }

      onUpdate(updatedRequirements)
      toast({
        title: "Requirements Updated",
        description: `${ROLE_DEFINITIONS[roleCode].name} requirement set to ${newCount}`,
      })
    } catch (error) {
      console.error('Error updating worker requirement:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update worker requirements",
        variant: "destructive",
      })
    } finally {
      setIsUpdating(false)
    }
  }

  const getRequiredCount = (roleCode: RoleCode): number => {
    return workerRequirements.find(req => req.roleCode === roleCode)?.requiredCount || 0
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Worker Requirements
        </CardTitle>
        <CardDescription>
          Configure how many workers of each type are needed for this shift
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {(Object.entries(ROLE_DEFINITIONS) as [RoleCode, typeof ROLE_DEFINITIONS[RoleCode]][]).map(([roleCode, roleDef]) => {
            const currentCount = getRequiredCount(roleCode)
            
            return (
              <div key={roleCode} className={`p-3 rounded-lg border ${roleDef.bgColor} ${roleDef.borderColor}`}>
                <div className="flex items-center justify-between mb-2">
                  <span className={`font-medium ${roleDef.color}`}>{roleDef.name}</span>
                  <Badge variant="outline">{roleCode}</Badge>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => updateWorkerRequirement(roleCode, currentCount - 1)}
                      disabled={currentCount === 0 || isUpdating}
                    >
                      <Minus className="h-3 w-3" />
                    </Button>
                    <span className="w-8 text-center font-medium">{currentCount}</span>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => updateWorkerRequirement(roleCode, currentCount + 1)}
                      disabled={isUpdating}
                    >
                      <Plus className="h-3 w-3" />
                    </Button>
                  </div>
                  <div className="flex items-center gap-2 justify-center">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => updateWorkerRequirement(roleCode, currentCount + 5)}
                      disabled={isUpdating}
                      className="text-xs px-2"
                    >
                      +5
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => updateWorkerRequirement(roleCode, currentCount + 10)}
                      disabled={isUpdating}
                      className="text-xs px-2"
                    >
                      +10
                    </Button>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}