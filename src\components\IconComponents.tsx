import React from 'react';
import { 
  Plus, 
  Sparkles, 
  Calendar, 
  Briefcase, 
  Users, 
  Building2, 
  Clock, 
  FileText, 
  Search,
  User,
  CalendarDays,
  UsersRound,

} from 'lucide-react';

interface IconProps {
  className?: string;
  size?: number;
}

// Primary action icons
export const PlusIcon: React.FC<IconProps> = ({ className = "h-5 w-5", size }) => (
  <Plus className={className} size={size} />
);

export const SparklesIcon: React.FC<IconProps> = ({ className = "h-5 w-5", size }) => (
  <Sparkles className={className} size={size} />
);

// Navigation icons
export const BriefcaseIcon: React.FC<IconProps> = ({ className = "h-5 w-5", size }) => (
  <Briefcase className={className} size={size} />
);

export const CalendarDaysIcon: React.FC<IconProps> = ({ className = "h-5 w-5", size }) => (
  <CalendarDays className={className} size={size} />
);

export const CalendarIcon: React.FC<IconProps> = ({ className = "h-5 w-5", size }) => (
  <Calendar className={className} size={size} />
);

export const UsersGroupIcon: React.FC<IconProps> = ({ className = "h-5 w-5", size }) => (
  <UsersRound className={className} size={size} />
);

export const UsersIcon: React.FC<IconProps> = ({ className = "h-5 w-5", size }) => (
  <Users className={className} size={size} />
);

export const BuildingOfficeIcon: React.FC<IconProps> = ({ className = "h-5 w-5", size }) => (
  <Building2 className={className} size={size} />
);

export const ClockIcon: React.FC<IconProps> = ({ className = "h-5 w-5", size }) => (
  <Clock className={className} size={size} />
);

export const DocumentTextIcon: React.FC<IconProps> = ({ className = "h-5 w-5", size }) => (
  <FileText className={className} size={size} />
);

// Utility icons
export const SearchIcon: React.FC<IconProps> = ({ className = "h-5 w-5", size }) => (
  <Search className={className} size={size} />
);

export const UserIcon: React.FC<IconProps> = ({ className = "h-5 w-5", size }) => (
  <User className={className} size={size} />
);

// Building icons for company/location
export const BuildingIcon: React.FC<IconProps> = ({ className = "h-5 w-5", size }) => (
  <Building2 className={className} size={size} />
);

// Export all icons as a collection for easy importing
export const Icons = {
  Plus: PlusIcon,
  Sparkles: SparklesIcon,
  Briefcase: BriefcaseIcon,
  Calendar: CalendarIcon,
  CalendarDays: CalendarDaysIcon,
  Users: UsersIcon,
  UsersGroup: UsersGroupIcon,
  Building: BuildingIcon,
  BuildingOffice: BuildingOfficeIcon,
  Clock: ClockIcon,
  DocumentText: DocumentTextIcon,
  Search: SearchIcon,
  User: UserIcon,
};

export default Icons;
