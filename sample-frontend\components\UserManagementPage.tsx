
import React, { useState, useMemo } from 'react';
import { User, UserRole, WorkerRole, WORKER_ROLE_DETAILS } from '../types';
import { SearchIcon, UsersGroupIcon } from './IconComponents';
import Avatar from './Avatar';

const UserManagementPage: React.FC<{ users: User[] }> = ({ users }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<UserRole | 'all'>('all');
  const [certFilter, setCertFilter] = useState<WorkerRole | 'all'>('all');

  const filteredUsers = useMemo(() => {
    return users
      .filter(user => {
        if (roleFilter === 'all') return true;
        return user.role === roleFilter;
      })
      .filter(user => {
        if (certFilter === 'all') return true;
        return user.certifications.includes(certFilter);
      })
      .filter(user =>
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
  }, [users, searchTerm, roleFilter, certFilter]);

  const FilterButton: React.FC<{ label: string; value: any; current: any; setter: (value: any) => void;}> = ({label, value, current, setter}) => {
    const base = "px-3 py-1.5 text-xs font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-indigo-500";
    const active = "bg-indigo-600 text-white shadow";
    const inactive = "bg-gray-700 text-gray-300 hover:bg-gray-600";
    return (
        <button onClick={() => setter(value)} className={`${base} ${current === value ? active : inactive}`}>
            {label}
        </button>
    )
  }

  return (
    <div className="bg-gray-800/50 rounded-xl p-4 sm:p-6 lg:p-8 border border-gray-700">
      <div className="flex flex-col sm:flex-row justify-between sm:items-center mb-6 gap-4">
        <div className="flex items-center gap-3">
            <UsersGroupIcon className="h-8 w-8 text-indigo-400" />
            <div>
                <h1 className="text-2xl font-bold text-white">User Management</h1>
                <p className="text-gray-400">Browse and manage all users in the system.</p>
            </div>
        </div>
        <div className="relative flex-grow max-w-sm">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <SearchIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
            type="text"
            placeholder="Search by name or email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-full bg-gray-700 border border-transparent rounded-md py-2.5 pl-10 pr-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
        </div>
      </div>
      
      <div className="mb-6 space-y-4">
        <div>
            <h3 className="text-sm font-semibold text-gray-300 mb-2">Filter by Role</h3>
            <div className="flex flex-wrap gap-2">
                <FilterButton label="All Roles" value="all" current={roleFilter} setter={setRoleFilter} />
                {Object.values(UserRole).map(role => (
                    <FilterButton key={role} label={role} value={role} current={roleFilter} setter={setRoleFilter} />
                ))}
            </div>
        </div>
         <div>
            <h3 className="text-sm font-semibold text-gray-300 mb-2">Filter by Certification</h3>
            <div className="flex flex-wrap gap-2">
                <FilterButton label="All Certs" value="all" current={certFilter} setter={setCertFilter} />
                {Object.values(WorkerRole).map(role => (
                     <FilterButton key={role} label={role} value={role} current={certFilter} setter={setCertFilter} />
                ))}
            </div>
        </div>
      </div>


      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-700">
          <thead className="bg-gray-800">
            <tr>
              <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-white sm:pl-6">Name</th>
              <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">Role</th>
              <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">Certifications</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-700/50">
            {filteredUsers.map(user => (
              <tr key={user.id} className="hover:bg-gray-700/40">
                <td className="py-4 pl-4 pr-3 text-sm sm:pl-6">
                  <div className="flex items-center">
                    <Avatar user={user} size="md" />
                    <div className="ml-4">
                      <div className="font-medium text-white">{user.name}</div>
                      <div className="text-gray-400">{user.email}</div>
                    </div>
                  </div>
                </td>
                <td className="px-3 py-4 text-sm text-gray-300">{user.role}</td>
                <td className="px-3 py-4 text-sm text-gray-300">
                    <div className="flex flex-wrap gap-1">
                        {user.certifications.map(cert => {
                            const details = WORKER_ROLE_DETAILS[cert];
                            return (
                                <span key={cert} title={details.name} className={`px-2 py-0.5 text-xs font-semibold text-white rounded-full ${details.colorClass}`}>
                                    {details.abbreviation}
                                </span>
                            )
                        })}
                    </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        {filteredUsers.length === 0 && (
            <div className="text-center py-12 text-gray-500">
                No users found matching your criteria.
            </div>
        )}
      </div>
    </div>
  );
};

export default UserManagementPage;
