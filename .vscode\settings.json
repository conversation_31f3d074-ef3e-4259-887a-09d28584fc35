{"typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.workingDirectories": ["."], "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/node_modules": true, "**/.next": true, "**/dist": true, "**/build": true}, "search.exclude": {"**/node_modules": true, "**/bower_components": true, "**/.next": true, "**/dist": true, "**/build": true}, "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "tailwindCSS.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]]}