
import React, { useState, useEffect, useCallback } from 'react';
import { Company, Job } from '../types';
import { generateJobWithAI } from '../services/geminiService';
import { XIcon } from './IconComponents';
import AILoadingState from './AILoadingState';

interface GenerateJobModalProps {
  onClose: () => void;
  onJobGenerated: (jobData: Omit<Job, 'id' | 'status' | 'company' | 'shifts'>) => void;
  companies: Company[];
}

const GenerateJobModal: React.FC<GenerateJobModalProps> = ({ onClose, onJobGenerated, companies }) => {
  const [prompt, setPrompt] = useState('');
  const [selectedCompanyId, setSelectedCompanyId] = useState<string>(companies[0]?.id || '');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isShowing, setIsShowing] = useState(false);

  const handleGenerate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!prompt.trim() || !selectedCompanyId) {
      setError("Please provide a prompt and select a company.");
      return;
    }

    setIsLoading(true);
    setError(null);

    const companyName = companies.find(c => c.id === selectedCompanyId)?.name || 'the company';
    const result = await generateJobWithAI(prompt, companyName);

    setIsLoading(false);

    if ('error' in result) {
      setError(result.error);
    } else {
      onJobGenerated({ ...result, companyId: selectedCompanyId });
      handleClose();
    }
  };

  const handleClose = () => {
    setIsShowing(false);
    setTimeout(onClose, 200); // Wait for animation
  };
  
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      handleClose();
    }
  }, [onClose]);

  useEffect(() => {
    setIsShowing(true);
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  return (
    <div
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
      className={`fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 transition-opacity duration-200 ${isShowing ? 'opacity-100' : 'opacity-0'}`}
      onClick={handleClose}
    >
      <div
        className={`relative w-[90vw] max-w-lg bg-gray-900 rounded-xl shadow-2xl transform transition-all duration-200 ${isShowing ? 'scale-100 opacity-100' : 'scale-95 opacity-0'}`}
        onClick={e => e.stopPropagation()}
      >
        <div className="bg-gray-800/50 p-6 sm:p-8 rounded-t-xl">
            <button
              onClick={handleClose}
              className="absolute top-4 right-4 text-gray-400 hover:text-white"
              aria-label="Close modal"
            >
              <XIcon />
            </button>

            <h2 id="modal-title" className="text-2xl font-bold text-white mb-2">
              Generate Job with AI
            </h2>
            <p className="text-gray-400">
              Describe the job you need. The AI will create a detailed job post.
            </p>
        </div>
        
        {isLoading ? (
            <AILoadingState />
        ) : (
            <form onSubmit={handleGenerate} className="p-6 sm:p-8">
            <div className="space-y-6">
                <div>
                <label htmlFor="company" className="block text-sm font-medium text-gray-300 mb-2">
                    Company
                </label>
                <select
                    id="company"
                    name="company"
                    value={selectedCompanyId}
                    onChange={e => setSelectedCompanyId(e.target.value)}
                    className="block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                    {companies.map(company => (
                    <option key={company.id} value={company.id}>{company.name}</option>
                    ))}
                </select>
                </div>

                <div>
                <label htmlFor="prompt" className="block text-sm font-medium text-gray-300 mb-2">
                    Job Requirements Prompt
                </label>
                <textarea
                    id="prompt"
                    name="prompt"
                    rows={5}
                    value={prompt}
                    onChange={e => setPrompt(e.target.value)}
                    placeholder="e.g., 'Need crew for a 3-day music festival setup. Must be able to lift 50 lbs and have experience with stage lighting.'"
                    className="block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm placeholder-gray-500"
                />
                </div>
            </div>
            
            {error && (
                <div className="mt-4 bg-red-900/50 border border-red-700 text-red-300 px-4 py-3 rounded-md text-sm">
                <p>{error}</p>
                </div>
            )}

            <div className="mt-8 flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-4">
                <button
                type="button"
                onClick={handleClose}
                disabled={isLoading}
                className="mt-4 sm:mt-0 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-white hover:bg-gray-600 focus:outline-none sm:w-auto sm:text-sm disabled:opacity-50"
                >
                Cancel
                </button>
                <button
                type="submit"
                disabled={isLoading || !prompt.trim() || !selectedCompanyId}
                className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    Generate Job
                </button>
            </div>
            </form>
        )}

      </div>
    </div>
  );
};

export default GenerateJobModal;