
import React from 'react';

interface WorkerRequirementInputProps {
  roleDetails: {
    name: string;
    abbreviation: string;
    colorClass: string;
  };
  count: number;
  onCountChange: (newCount: number) => void;
}

const WorkerRequirementInput: React.FC<WorkerRequirementInputProps> = ({ roleDetails, count, onCountChange }) => {
  
  const handleUpdate = (amount: number) => {
    onCountChange(Math.max(0, count + amount));
  };
  
  const StepperButton: React.FC<{ amount: number, children: React.ReactNode, size?: 'normal' | 'small' }> = ({ amount, children, size = 'normal' }) => (
    <button
        type="button"
        onClick={() => handleUpdate(amount)}
        disabled={count + amount < 0}
        className={`flex items-center justify-center rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-indigo-500
        ${size === 'normal' 
            ? 'w-10 h-10 bg-gray-700 text-gray-300 hover:bg-gray-600' 
            : 'w-8 h-8 bg-gray-700/50 text-xs text-gray-400 hover:bg-gray-600'
        }`}
    >
      {children}
    </button>
  );

  return (
    <div className="bg-gray-800 p-3 rounded-lg flex items-center justify-between">
      <div className="flex items-center gap-3">
        <span className={`flex-shrink-0 w-8 h-8 rounded-full ${roleDetails.colorClass} flex items-center justify-center text-xs font-bold text-white`}>
          {roleDetails.abbreviation}
        </span>
        <span className="font-medium text-white">{roleDetails.name}</span>
      </div>
      <div className="flex items-center gap-1">
        <StepperButton amount={-5} size="small">-5</StepperButton>
        <StepperButton amount={-1}>-</StepperButton>
        <span className="w-10 text-center text-lg font-bold text-white tabular-nums">{count}</span>
        <StepperButton amount={1}>+</StepperButton>
        <StepperButton amount={5} size="small">+5</StepperButton>
      </div>
    </div>
  );
};

export default WorkerRequirementInput;