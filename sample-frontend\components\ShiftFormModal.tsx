
import React, { useState, useEffect, useCallback } from 'react';
import { Shift, Job, WorkerRole, WORKER_ROLE_DETAILS } from '../types';
import { XIcon, UsersGroupIcon } from './IconComponents';
import WorkerRequirementInput from './WorkerRequirementInput';

interface ShiftFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: { date: string; startTime: string; endTime: string; roleCounts: Record<WorkerRole, number> }) => void;
  onDelete: (shiftId: string) => void;
  shift: Shift | null;
  job: Job | undefined;
}

const allRoles = Object.keys(WORKER_ROLE_DETAILS) as WorkerRole[];
const initialRoleCounts = allRoles.reduce((acc, role) => {
    acc[role] = 0;
    return acc;
}, {} as Record<WorkerRole, number>);

const ShiftFormModal: React.FC<ShiftFormModalProps> = ({ isOpen, onClose, onSave, onDelete, shift, job }) => {
  const [date, setDate] = useState('');
  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');
  const [roleCounts, setRoleCounts] = useState<Record<WorkerRole, number>>(initialRoleCounts);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isShowing, setIsShowing] = useState(false);

  useEffect(() => {
    if (isOpen) {
        setIsShowing(true);
        if (shift) {
            // Editing mode
            setDate(shift.date);
            setStartTime(shift.startTime);
            setEndTime(shift.endTime);
            
            const counts = shift.assignments.reduce((acc, assignment) => {
                acc[assignment.role] = (acc[assignment.role] || 0) + 1;
                return acc;
            }, {} as Record<WorkerRole, number>);
            
            // Ensure all roles are present in the state
            const fullCounts = { ...initialRoleCounts, ...counts };
            setRoleCounts(fullCounts);

        } else {
            // Creation mode - reset form
            setDate(new Date().toISOString().split('T')[0]);
            setStartTime('09:00');
            setEndTime('17:00');
            setRoleCounts(initialRoleCounts);
        }
        setShowDeleteConfirm(false);
    }
  }, [shift, isOpen]);

  const handleRoleCountChange = (role: WorkerRole, newCount: number) => {
    if (newCount >= 0) {
      setRoleCounts(prev => ({ ...prev, [role]: newCount }));
    }
  };

  const handleClose = () => {
    setIsShowing(false);
    setTimeout(onClose, 200);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({ date, startTime, endTime, roleCounts });
  };
  
  const handleDelete = () => {
      if(shift) {
          onDelete(shift.id);
      }
  }

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      handleClose();
    }
  }, [onClose]);

  useEffect(() => {
    if (isOpen) {
        document.addEventListener('keydown', handleKeyDown);
    }
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown, isOpen]);
  
  if (!isOpen) return null;

  return (
    <div
      role="dialog"
      aria-modal="true"
      aria-labelledby="shift-form-title"
      className={`fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 transition-opacity duration-200 ${isShowing ? 'opacity-100' : 'opacity-0'}`}
      onClick={handleClose}
    >
      <div
        className={`relative w-[90vw] max-w-2xl bg-gray-800 rounded-xl shadow-2xl transform transition-all duration-200 flex flex-col max-h-[90vh] ${isShowing ? 'scale-100 opacity-100' : 'scale-95 opacity-0'}`}
        onClick={e => e.stopPropagation()}
      >
        <div className='flex-shrink-0 p-6 sm:p-8 rounded-t-xl bg-gray-800/50'>
            <button
            onClick={handleClose}
            className="absolute top-4 right-4 text-gray-400 hover:text-white"
            aria-label="Close modal"
            >
            <XIcon />
            </button>
            <h2 id="shift-form-title" className="text-2xl font-bold text-white mb-2">
            {shift ? 'Edit Shift' : 'Create New Shift'}
            </h2>
            <p className="text-gray-400">For job: <span className="font-semibold text-gray-300">{job?.name}</span></p>
        </div>

        <form onSubmit={handleSubmit} className="flex-grow overflow-y-auto px-6 sm:px-8 space-y-6 pt-6">
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div className="sm:col-span-1">
              <label htmlFor="date" className="block text-sm font-medium text-gray-300 mb-1">Date</label>
              <input type="date" id="date" value={date} onChange={e => setDate(e.target.value)} required className="block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"/>
            </div>
            <div className="sm:col-span-1">
              <label htmlFor="startTime" className="block text-sm font-medium text-gray-300 mb-1">Start Time</label>
              <input type="time" id="startTime" value={startTime} onChange={e => setStartTime(e.target.value)} required className="block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"/>
            </div>
            <div className="sm:col-span-1">
              <label htmlFor="endTime" className="block text-sm font-medium text-gray-300 mb-1">End Time</label>
              <input type="time" id="endTime" value={endTime} onChange={e => setEndTime(e.target.value)} required className="block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"/>
            </div>
          </div>
          
          <div>
            <div className="flex items-center gap-3 mb-4">
              <UsersGroupIcon className="h-6 w-6 text-gray-400"/>
              <div>
                 <h3 className="text-lg font-semibold text-white">Worker Requirements</h3>
                 <p className="text-sm text-gray-400">Configure how many workers of each type are needed for this shift.</p>
              </div>
            </div>
            <div className="p-4 bg-gray-900/50 rounded-lg">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {allRoles.map(role => (
                    <WorkerRequirementInput
                        key={role}
                        roleDetails={WORKER_ROLE_DETAILS[role]}
                        count={roleCounts[role]}
                        onCountChange={(newCount) => handleRoleCountChange(role, newCount)}
                    />
                ))}
                </div>
            </div>
          </div>
        </form>
        <div className="mt-8 p-6 pt-6 border-t border-gray-700 flex flex-col sm:flex-row justify-between items-center gap-4 flex-shrink-0 bg-gray-800/50 rounded-b-xl">
          <div>
            {shift && (
              <>
                {!showDeleteConfirm ? (
                  <button
                    type="button"
                    onClick={() => setShowDeleteConfirm(true)}
                    className="text-sm text-red-400 hover:text-red-300 hover:underline"
                  >
                    Delete Shift
                  </button>
                ) : (
                  <div className="flex items-center gap-2">
                     <button
                        type="button"
                        onClick={handleDelete}
                        className="px-3 py-1 rounded-md bg-red-600 hover:bg-red-700 text-white text-sm font-semibold"
                    >
                        Confirm Delete
                    </button>
                    <button
                        type="button"
                        onClick={() => setShowDeleteConfirm(false)}
                        className="text-sm text-gray-400 hover:text-white"
                    >
                        Cancel
                    </button>
                  </div>
                )}
              </>
            )}
          </div>
          <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-4 w-full sm:w-auto">
            <button
              type="button"
              onClick={handleClose}
              className="mt-2 sm:mt-0 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-white hover:bg-gray-600 sm:w-auto sm:text-sm"
            >
              Cancel
            </button>
            <button
              type="submit"
              onClick={handleSubmit}
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 sm:w-auto sm:text-sm"
            >
              Save Shift
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShiftFormModal;