'use client';

import { useUser } from '@/hooks/use-user';
import { useApi } from '@/hooks/use-api';
import Header from '@/components/Header';
import JobCard from '@/components/JobCard';
import ShiftCard from '@/components/ShiftCard';
import { PlusIcon, BriefcaseIcon, CalendarIcon, UsersIcon } from '@/components/IconComponents';
import { Job, Shift } from '@prisma/client';

type DashboardData = {
  activeJobsCount: number;
  upcomingShiftsCount: number;
  completedShiftsCount: number;
  recentJobs: Job[];
  upcomingShifts: (Shift & { job: Job })[];
};

// Convert Prisma job to JobCard format
const convertJobForCard = (job: Job) => ({
  id: job.id,
  name: job.name,
  description: job.description || '',
  status: job.status as 'Pending' | 'Active' | 'Completed' | 'On Hold',
  companyId: job.companyId,
  company: {
    id: job.companyId,
    name: 'Your Company' // Client dashboard shows their own jobs
  },
  requestedWorkers: 0, // This would need to be calculated from shifts
  shifts: [] // Simplified for card view
});

// Convert Prisma shift to ShiftCard format
const convertShiftForCard = (shift: Shift & { job: Job }) => ({
  id: shift.id,
  jobName: shift.job.name,
  companyName: 'Your Company',
  date: shift.date.toISOString().split('T')[0],
  startTime: shift.startTime,
  endTime: shift.endTime,
  status: shift.status,
  assignments: [] // Client dashboard doesn't need assignment details
});

export default function ClientDashboard() {
  const { user } = useUser();
  const { data, loading, error } = useApi<DashboardData>(
    user?.companyId ? `/api/clients/${user.companyId}/dashboard` : null
  );

  const handleJobClick = (job: any) => {
    // Navigate to job details or handle job selection
    console.log('Job clicked:', job);
  };

  const handleShiftClick = (shift: any) => {
    // Navigate to shift details or handle shift selection
    console.log('Shift clicked:', shift);
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div className="text-red-500">Error loading dashboard data.</div>;
  }

  const recentJobs = data?.recentJobs || [];
  const upcomingShifts = data?.upcomingShifts || [];

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100 font-sans">
      <Header>
        <button className="sample-btn-primary">
          <PlusIcon />
          <span>Request<span className="hidden sm:inline"> Workers</span></span>
        </button>
      </Header>
      <main className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto">
          {/* Welcome Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-white mb-2">Welcome, {user?.name}!</h1>
            <p className="text-gray-400">{user?.companyName}</p>
          </div>

          {/* Stats Cards */}
          <div className="grid gap-4 md:grid-cols-3 mb-8">
            <div className="sample-card-static bg-gradient-to-r from-blue-600 to-blue-700 text-white border-0">
              <div className="p-6 flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm">Active Jobs</p>
                  <p className="text-3xl font-bold">{data?.activeJobsCount || 0}</p>
                </div>
                <BriefcaseIcon className="h-8 w-8 text-blue-200" />
              </div>
            </div>
            <div className="sample-card-static bg-gradient-to-r from-green-600 to-green-700 text-white border-0">
              <div className="p-6 flex items-center justify-between">
                <div>
                  <p className="text-green-100 text-sm">Upcoming Shifts</p>
                  <p className="text-3xl font-bold">{data?.upcomingShiftsCount || 0}</p>
                </div>
                <CalendarIcon className="h-8 w-8 text-green-200" />
              </div>
            </div>
            <div className="sample-card-static bg-gradient-to-r from-purple-600 to-purple-700 text-white border-0">
              <div className="p-6 flex items-center justify-between">
                <div>
                  <p className="text-purple-100 text-sm">Completed Shifts</p>
                  <p className="text-3xl font-bold">{data?.completedShiftsCount || 0}</p>
                </div>
                <UsersIcon className="h-8 w-8 text-purple-200" />
              </div>
            </div>
          </div>

          {/* Recent Jobs */}
          <div className="grid gap-6 md:grid-cols-2 mb-8">
            <div className="sample-card-static">
              <div className="p-6">
                <h2 className="text-xl font-semibold text-white mb-6 pb-2 border-b border-gray-700">
                  Recent Jobs
                </h2>
                {recentJobs.length > 0 ? (
                  <div className="grid grid-cols-1 gap-4">
                    {recentJobs.slice(0, 3).map((job) => (
                      <JobCard
                        key={job.id}
                        job={convertJobForCard(job)}
                        onClick={handleJobClick}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="sample-empty-state">
                    <BriefcaseIcon className="mx-auto h-12 w-12 text-indigo-400" />
                    <h3 className="mt-4 text-xl font-semibold text-white">No Recent Jobs</h3>
                    <p className="text-gray-400 mt-2 max-w-md mx-auto">You haven't created any jobs yet. Start by requesting workers for your projects.</p>
                  </div>
                )}
              </div>
            </div>

            <div className="sample-card-static">
              <div className="p-6">
                <h2 className="text-xl font-semibold text-white mb-6 pb-2 border-b border-gray-700">
                  Upcoming Shifts
                </h2>
                {upcomingShifts.length > 0 ? (
                  <div className="grid grid-cols-1 gap-4">
                    {upcomingShifts.slice(0, 3).map((shift) => (
                      <ShiftCard
                        key={shift.id}
                        shift={convertShiftForCard(shift)}
                        onClick={handleShiftClick}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="sample-empty-state">
                    <CalendarIcon className="mx-auto h-12 w-12 text-indigo-400" />
                    <h3 className="mt-4 text-xl font-semibold text-white">No Upcoming Shifts</h3>
                    <p className="text-gray-400 mt-2 max-w-md mx-auto">No shifts are scheduled. Create jobs to get started with workforce management.</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
