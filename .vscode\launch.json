{"version": "0.2.0", "configurations": [{"name": "Next.js: debug server-side", "type": "node", "request": "attach", "port": 9229, "skipFiles": ["<node_internals>/**"]}, {"name": "Next.js: debug client-side", "type": "chrome", "request": "launch", "url": "http://localhost:3001", "webRoot": "${workspaceFolder}"}, {"name": "Next.js: debug full stack", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/next", "args": ["dev"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"], "env": {"NODE_OPTIONS": "--inspect"}}, {"name": "Debug API Route", "type": "node", "request": "attach", "port": 9229, "skipFiles": ["<node_internals>/**"], "localRoot": "${workspaceFolder}", "remoteRoot": "${workspaceFolder}"}]}