
import React from 'react';
import { SparklesIcon, CalendarDaysIcon, ClockIcon, BriefcaseIcon, UsersGroupIcon } from './IconComponents';

const DocsPage: React.FC = () => {
    
  const FeatureSection: React.FC<{icon: React.ReactNode, title: string, children: React.ReactNode}> = ({ icon, title, children }) => (
    <div className="bg-gray-800 p-6 rounded-lg border border-gray-700/50">
        <div className="flex items-center gap-4 mb-4">
            {icon}
            <h3 className="text-xl font-bold text-white">{title}</h3>
        </div>
        <div className="space-y-3 text-gray-300 text-base leading-relaxed">
            {children}
        </div>
    </div>
  );

  return (
    <div className="bg-gray-800/50 rounded-xl p-4 sm:p-6 lg:p-8 border border-gray-700">
        <div className="text-center mb-10">
            <h1 className="text-4xl font-extrabold text-white tracking-tight mb-3">Welcome to the Dashboard!</h1>
            <p className="max-w-3xl mx-auto text-lg text-gray-400">
                This guide will help you get started and make the most out of the powerful features available for managing your workforce.
            </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <FeatureSection 
                icon={<SparklesIcon className="w-8 h-8 text-indigo-400" />} 
                title="AI Job Generation"
            >
                <p>
                    Create new job postings in seconds. Click the <span className="font-semibold text-indigo-300">"Generate Job with AI"</span> button in the header.
                </p>
                <p>
                    Provide a simple prompt describing your needs (e.g., "three days of festival setup, need forklift operators"). The AI will generate a professional job name, a detailed description, and a recommended number of workers.
                </p>
                <p>
                    Just select the client company, and a new job will be added to the board, ready for you to start creating shifts.
                </p>
            </FeatureSection>
            
            <FeatureSection
                icon={<BriefcaseIcon className="w-8 h-8 text-indigo-400" />}
                title="Managing Jobs"
            >
                <p>
                    The "Jobs" tab is your main dashboard. It shows all jobs, which you can filter by status (Active, Completed, etc.) and search by name, company, or description.
                </p>
                <p>
                    Click on any job card to open the Job Detail view. From here, you can see all associated shifts, view the full job description, and add new shifts.
                </p>
            </FeatureSection>

            <FeatureSection
                icon={<CalendarDaysIcon className="w-8 h-8 text-indigo-400" />}
                title="Managing Shifts"
            >
                <p>
                    From the Job Detail view, you can add or view shifts. Clicking a shift opens the Shift Detail modal, the central hub for managing a specific workday.
                </p>
                <p>
                    In the Shift Detail modal, you can assign workers to specific roles. The dropdowns are automatically filtered to only show workers with the required certifications for that role.
                </p>
                <p>
                    Click <span className="font-semibold text-gray-200">"Edit Shift"</span> to change the date, time, or the number of workers required for each role.
                </p>
            </FeatureSection>
            
            <FeatureSection
                icon={<ClockIcon className="w-8 h-8 text-indigo-400" />}
                title="Time Tracking"
            >
                <p>
                    Once a worker is assigned to a shift, time tracking controls appear. Workers can start their shift, clock out for breaks, and clock back in.
                </p>
                <p>
                   A worker's status is always visible: <span className="text-gray-400">Ready to Start</span>, <span className="text-yellow-400">Clocked In</span>, <span className="text-gray-400">On Break</span>, or <span className="text-green-400">Completed</span>.
                </p>
                <p>
                    The <span className="text-red-400">"End Shift"</span> button finalizes a worker's time for the day, clocks them out if necessary, and locks their time entries.
                </p>
            </FeatureSection>
            
             <FeatureSection
                icon={<UsersGroupIcon className="w-8 h-8 text-indigo-400" />}
                title="Other Tabs"
            >
                <p>
                    <span className="font-bold text-white">Users:</span> Browse and filter all workers in the system by their role or certifications.
                </p>
                 <p>
                    <span className="font-bold text-white">Clients:</span> See a directory of all your client companies and a summary of their job history.
                </p>
                 <p>
                    <span className="font-bold text-white">Timesheets:</span> A powerful tool to review and process worker hours within a specific date range.
                </p>
                 <p>
                    <span className="font-bold text-white">Import/Export:</span> Download your jobs, shifts, or timesheet data as CSV files for payroll or reporting.
                </p>
            </FeatureSection>
        </div>
    </div>
  );
};

export default DocsPage;
