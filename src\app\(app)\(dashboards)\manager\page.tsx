'use client';

import { useUser } from '@/hooks/use-user';
import { useApi } from '@/hooks/use-api';
import { useEffect, useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import TabNavigation, { TabName } from '@/components/TabNavigation';
import SearchAndFilter from '@/components/SearchAndFilter';
import ShiftCard from '@/components/ShiftCard';
import JobCard from '@/components/JobCard';
import { PlusIcon, SparklesIcon } from '@/components/IconComponents';
import ShiftManager from '@/components/dashboard/shift-management/shift-manager';
import { ShiftWithDetails } from '@/components/dashboard/shift-management/types';

type Shift = {
  id: string;
  jobName: string;
  companyName: string;
  date: string;
  startTime: string;
  endTime: string;
  status: string;
  assignments: {
    role: 'CC' | 'SH' | 'FO' | 'RFO' | 'RG' | 'GL';
    userId: string | null;
  }[];
};

type Job = {
  id: string;
  name: string;
  description: string;
  status: 'Pending' | 'Active' | 'Completed' | 'On Hold';
  companyId: string;
  company: {
    id: string;
    name: string;
  };
  requestedWorkers: number;
  shifts: {
    id: string;
    status: 'Upcoming' | 'In Progress' | 'Completed';
  }[];
};

export default function ManagerDashboard() {
  const { user } = useUser();
  const router = useRouter();
  const [expandedShift, setExpandedShift] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<TabName>('shifts');
  const [filterStatus, setFilterStatus] = useState<'Pending' | 'Active' | 'Completed' | 'On Hold' | 'all'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  const { data: shiftsData, loading: shiftsLoading, error: shiftsError, refetch: refetchShifts } = useApi<{ shifts: Shift[] }>('/api/shifts?filter=active');
  const { data: jobsData, loading: jobsLoading, error: jobsError, refetch: refetchJobs } = useApi<{ jobs: Job[] }>('/api/jobs');
  const { data: shiftDetails, loading: shiftDetailsLoading, error: shiftDetailsError, refetch: refetchShiftDetails } = useApi<ShiftWithDetails>(
    expandedShift ? `/api/shifts/${expandedShift}/details` : null
  );

  useEffect(() => {
    refetchShifts();
    refetchJobs();
  }, [refetchShifts, refetchJobs]);

  const handleToggleShift = (shiftId: string) => {
    if (expandedShift === shiftId) {
      setExpandedShift(null);
    } else {
      setExpandedShift(shiftId);
    }
  };

  const handleShiftClick = (shift: Shift) => {
    handleToggleShift(shift.id);
  };

  const handleJobClick = (job: Job) => {
    // Navigate to job details or handle job selection
    console.log('Job clicked:', job);
  };

  if (shiftsLoading || jobsLoading) {
    return <div>Loading dashboard data...</div>;
  }

  if (shiftsError || jobsError) {
    return <div className="text-red-500">Error loading dashboard data.</div>;
  }

  const shifts = shiftsData?.shifts || [];
  const jobs = jobsData?.jobs || [];

  // Filter jobs based on search and status
  const filteredJobs = useMemo(() => {
    return jobs
      .filter(job => {
        if (filterStatus === 'all') return true;
        return job.status === filterStatus;
      })
      .filter(job =>
        job.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.company.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
  }, [jobs, filterStatus, searchTerm]);

  // Categorize shifts
  const categorizedShifts = useMemo(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const todaysShifts: Shift[] = [];
    const upcomingShifts: Shift[] = [];
    const pastShifts: Shift[] = [];

    shifts
      .filter(shift =>
        shift.jobName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        shift.companyName.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
      .forEach(shift => {
        const shiftDate = new Date(shift.date);
        shiftDate.setHours(0,0,0,0);

        if (shiftDate.getTime() === today.getTime()) {
            todaysShifts.push(shift);
        } else if (shiftDate > today) {
            upcomingShifts.push(shift);
        } else {
            pastShifts.push(shift);
        }
      });

      return { todaysShifts, upcomingShifts, pastShifts: pastShifts.sort((a,b) => new Date(b.date).getTime() - new Date(a.date).getTime()) };
  }, [shifts, searchTerm]);

  const renderShiftGroup = (title: string, shifts: Shift[]) => {
    if (shifts.length === 0) return null;
    return (
        <div className="mb-10">
            <h2 className="text-xl font-semibold text-white mb-4 pb-2 border-b border-gray-700">{title}</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {shifts.map(shift => (
                <ShiftCard key={shift.id} shift={shift} onClick={() => handleShiftClick(shift)} />
              ))}
            </div>
        </div>
    )
  };

  const renderContent = () => {
    switch(activeTab) {
      case 'jobs':
        return (
          <>
            <SearchAndFilter
                currentFilter={filterStatus}
                onFilterChange={setFilterStatus}
                onSearchChange={setSearchTerm}
                isShiftView={false}
            />
            {filteredJobs.length > 0 ? (
              <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredJobs.map(job => (
                  <JobCard key={job.id} job={job} onClick={handleJobClick} />
                ))}
              </div>
            ) : (
              <div className="sample-empty-state">
                  <SparklesIcon className="mx-auto h-12 w-12 text-indigo-400" />
                  <h3 className="mt-4 text-xl font-semibold text-white">No Jobs Found</h3>
                  <p className="text-gray-400 mt-2 mb-6 max-w-md mx-auto">No jobs match your current filters. Try adjusting your search or create a new job.</p>
              </div>
            )}
          </>
        );
      case 'shifts':
        const { todaysShifts, upcomingShifts, pastShifts } = categorizedShifts;
        const noShiftsFound = todaysShifts.length === 0 && upcomingShifts.length === 0 && pastShifts.length === 0;
        return (
            <>
               <SearchAndFilter
                   onSearchChange={setSearchTerm}
                   isShiftView={true}
               />
               <div className="mt-6">
                   {noShiftsFound ? (
                       <div className="sample-empty-state">
                           <SparklesIcon className="mx-auto h-12 w-12 text-indigo-400" />
                           <h3 className="mt-4 text-xl font-semibold text-white">No Shifts Found</h3>
                           <p className="text-gray-400 mt-2 max-w-md mx-auto">No shifts match your current search. Try a different search term.</p>
                       </div>
                   ) : (
                       <>
                           {renderShiftGroup("Today's Shifts", todaysShifts)}
                           {renderShiftGroup("Upcoming Shifts", upcomingShifts)}
                           {renderShiftGroup("Past Shifts", pastShifts)}
                       </>
                   )}
               </div>
            </>
        );
      default:
        return (
          <div className="sample-empty-state">
            <SparklesIcon className="mx-auto h-12 w-12 text-indigo-400" />
            <h3 className="mt-4 text-xl font-semibold text-white">Coming Soon</h3>
            <p className="text-gray-400 mt-2 max-w-md mx-auto">This section is under development.</p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100 font-sans">
      <Header>
        <button className="sample-btn-primary">
            <PlusIcon />
            <span>Create Job<span className="hidden sm:inline"> or Shift</span></span>
        </button>
      </Header>
      <main className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto">
            <TabNavigation
              activeTab={activeTab}
              onTabChange={setActiveTab}
              availableTabs={['jobs', 'shifts', 'users', 'clients', 'timesheets']}
            />

            <div className="mt-6">
                {renderContent()}
            </div>
        </div>
      </main>

      {/* Shift Details Modal */}
      {expandedShift && shiftDetails && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="sample-card-static max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold text-white">Shift Details</h2>
                <button
                  onClick={() => setExpandedShift(null)}
                  className="text-gray-400 hover:text-white"
                >
                  ×
                </button>
              </div>
              <ShiftManager
                shift={shiftDetails}
                onUpdate={() => {
                  refetchShifts();
                  refetchShiftDetails();
                }}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
